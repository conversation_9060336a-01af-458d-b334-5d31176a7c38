export const openicon = `
<svg width="24" height="23" viewBox="0 0 24 23" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21.3468 14.4501V8.8251C21.3468 4.1376 19.4718 2.2626 14.7843 2.2626H9.1593C4.4718 2.2626 2.5968 4.1376 2.5968 8.8251V14.4501C2.5968 19.1376 4.4718 21.0126 9.1593 21.0126H14.7843C19.4718 21.0126 21.3468 19.1376 21.3468 14.4501Z" stroke="white" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8.2218 2.2626V21.0126" stroke="white" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12.3845 9.23837L14.7845 11.6384L12.3845 14.0384" stroke="white" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`;

export const closeicon = `
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.03 9L2.03 15C2.03 20 4.03 22 9.03 22L15.03 22C20.03 22 22.03 20 22.03 15L22.03 9C22.03 4 20.03 2 15.03 2L9.03 2C4.03 2 2.03 4 2.03 9Z" stroke="#5F9EA0" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.03 22L9.03 2" stroke="#5F9EA0" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16.03 14.5601L13.47 12.0001L16.03 9.44006" stroke="#5F9EA0" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`;

export const addicon = `
<svg width="20" height="20" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M6.5 8H0.5V6H6.5V0H8.5V6H14.5V8H8.5V14H6.5V8Z" fill="white"/>
</svg>
`;


export const portalicon = `
<svg width="16" height="16" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.95 20.375L16 17.425V19.65H14V14H19.65V16H17.4L20.35 18.95L18.95 20.375ZM10 20C8.61667 20 7.31667 19.7375 6.1 19.2125C4.88333 18.6875 3.825 17.975 2.925 17.075C2.025 16.175 1.3125 15.1167 0.7875 13.9C0.2625 12.6833 0 11.3833 0 10C0 8.61667 0.2625 7.31667 0.7875 6.1C1.3125 4.88333 2.025 3.825 2.925 2.925C3.825 2.025 4.88333 1.3125 6.1 0.7875C7.31667 0.2625 8.61667 0 10 0C11.3833 0 12.6833 0.2625 13.9 0.7875C15.1167 1.3125 16.175 2.025 17.075 2.925C17.975 3.825 18.6875 4.88333 19.2125 6.1C19.7375 7.31667 20 8.61667 20 10C20 10.3333 19.9833 10.6667 19.95 11C19.9167 11.3333 19.8667 11.6667 19.8 12H17.75C17.8333 11.6667 17.8958 11.3333 17.9375 11C17.9792 10.6667 18 10.3333 18 10C18 9.66667 17.9792 9.33333 17.9375 9C17.8958 8.66667 17.8333 8.33333 17.75 8H14.35C14.4 8.33333 14.4375 8.66667 14.4625 9C14.4875 9.33333 14.5 9.66667 14.5 10C14.5 10.3333 14.4875 10.6667 14.4625 11C14.4375 11.3333 14.4 11.6667 14.35 12H12.35C12.4 11.6667 12.4375 11.3333 12.4625 11C12.4875 10.6667 12.5 10.3333 12.5 10C12.5 9.66667 12.4875 9.33333 12.4625 9C12.4375 8.66667 12.4 8.33333 12.35 8H7.65C7.6 8.33333 7.5625 8.66667 7.5375 9C7.5125 9.33333 7.5 9.66667 7.5 10C7.5 10.3333 7.5125 10.6667 7.5375 11C7.5625 11.3333 7.6 11.6667 7.65 12H11V14H8.1C8.3 14.7167 8.55833 15.4042 8.875 16.0625C9.19167 16.7208 9.56667 17.35 10 17.95C10.3333 17.95 10.6667 17.9292 11 17.8875C11.3333 17.8458 11.6667 17.8083 12 17.775V19.825C11.6667 19.8583 11.3333 19.8958 11 19.9375C10.6667 19.9792 10.3333 20 10 20ZM2.25 12H5.65C5.6 11.6667 5.5625 11.3333 5.5375 11C5.5125 10.6667 5.5 10.3333 5.5 10C5.5 9.66667 5.5125 9.33333 5.5375 9C5.5625 8.66667 5.6 8.33333 5.65 8H2.25C2.16667 8.33333 2.10417 8.66667 2.0625 9C2.02083 9.33333 2 9.66667 2 10C2 10.3333 2.02083 10.6667 2.0625 11C2.10417 11.3333 2.16667 11.6667 2.25 12ZM3.1 6H6.05C6.2 5.38333 6.3875 4.77917 6.6125 4.1875C6.8375 3.59583 7.1 3.01667 7.4 2.45C6.48333 2.75 5.65833 3.20417 4.925 3.8125C4.19167 4.42083 3.58333 5.15 3.1 6ZM7.4 17.55C7.1 16.9833 6.8375 16.4042 6.6125 15.8125C6.3875 15.2208 6.2 14.6167 6.05 14H3.1C3.58333 14.85 4.19167 15.5792 4.925 16.1875C5.65833 16.7958 6.48333 17.25 7.4 17.55ZM8.1 6H11.9C11.7 5.28333 11.4417 4.59583 11.125 3.9375C10.8083 3.27917 10.4333 2.65 10 2.05C9.56667 2.65 9.19167 3.27917 8.875 3.9375C8.55833 4.59583 8.3 5.28333 8.1 6ZM13.95 6H16.9C16.4167 5.15 15.8083 4.42083 15.075 3.8125C14.3417 3.20417 13.5167 2.75 12.6 2.45C12.9 3.01667 13.1625 3.59583 13.3875 4.1875C13.6125 4.77917 13.8 5.38333 13.95 6Z" fill="#1C1B1F"/>
</svg>
`;

export const closepluginicon = `
<svg width="12" height="12" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1.4 14L0 12.6L5.6 7L0 1.4L1.4 0L7 5.6L12.6 0L14 1.4L8.4 7L14 12.6L12.6 14L7 8.4L1.4 14Z" fill="#1C1B1F"/>
</svg>
`;

export const backicon = `
<svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M6 12L0 6L6 0L7.4 1.4L2.8 6L7.4 10.6L6 12Z" fill="#1C1B1F"/>
</svg>
`;

export const touricon = `
<svg width="30" height="30" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M17.6737 24.5L16.2813 23.1077L18.3813 20.9743L16.2813 18.8743L17.6737 17.4487L19.807 19.582L21.907 17.4487L23.3327 18.8743L21.1993 20.9743L23.3327 23.1077L21.907 24.5L19.807 22.4L17.6737 24.5ZM4.33268 22.5C4.79602 22.5 5.18957 22.338 5.51335 22.014C5.83735 21.6902 5.99935 21.2967 5.99935 20.8333C5.99935 20.37 5.83735 19.9764 5.51335 19.6527C5.18957 19.3287 4.79602 19.1667 4.33268 19.1667C3.86935 19.1667 3.47579 19.3287 3.15202 19.6527C2.82802 19.9764 2.66602 20.37 2.66602 20.8333C2.66602 21.2967 2.82802 21.6902 3.15202 22.014C3.47579 22.338 3.86935 22.5 4.33268 22.5ZM4.33268 24.5C3.31557 24.5 2.45024 24.1431 1.73668 23.4293C1.0229 22.7158 0.666016 21.8504 0.666016 20.8333C0.666016 19.8162 1.0229 18.9509 1.73668 18.2373C2.45024 17.5236 3.31557 17.1667 4.33268 17.1667C5.13779 17.1667 5.86213 17.403 6.50568 17.8757C7.14924 18.3483 7.58813 18.9736 7.82235 19.7513C8.77457 19.5838 9.56346 19.1317 10.189 18.395C10.8148 17.6581 11.1277 16.8042 11.1277 15.8333V10.6923C11.1277 8.94189 11.7456 7.44878 12.9813 6.213C14.2173 4.977 15.7106 4.359 17.461 4.359H19.4813L17.048 1.92567L18.4737 0.5L23.3327 5.359L18.4737 10.218L17.048 8.82567L19.4687 6.359H17.461C16.2559 6.359 15.2323 6.77989 14.3903 7.62167C13.5486 8.46367 13.1277 9.48722 13.1277 10.6923V15.8333C13.1277 17.3444 12.6247 18.6611 11.6187 19.7833C10.6127 20.9056 9.3609 21.5683 7.86335 21.7717C7.65668 22.5768 7.22468 23.2328 6.56735 23.7397C5.91002 24.2466 5.16513 24.5 4.33268 24.5ZM2.05835 8.88467L0.666016 7.49233L2.76602 5.359L0.666016 3.259L2.05835 1.83333L4.19168 3.96667L6.29168 1.83333L7.71735 3.259L5.58402 5.359L7.71735 7.49233L6.29168 8.88467L4.19168 6.78467L2.05835 8.88467Z" fill="#5F9EA0"/>
</svg>
`;

export const announcementicon = `
<svg width="46" height="39" viewBox="0 0 46 39" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="0.5" y="0.5" width="45" height="38" rx="1.5" fill="#EFEFEF" stroke="#5F9EA0"/>
<path d="M1 2C1 1.44771 1.44772 1 2 1H44C44.5523 1 45 1.44772 45 2V20H1V2Z" fill="#A7C7C8"/>
<rect x="24" y="32" width="16" height="4" fill="#5F9EA0"/>
<rect x="6.1" y="32.1" width="15.8" height="3.8" fill="#A7C7C8" stroke="#5F9EA0" stroke-width="0.2"/>
<rect x="7" y="22" width="31" height="3" rx="1" fill="#D9D9D9"/>
<rect x="10" y="26" width="25" height="1" rx="0.5" fill="#D9D9D9"/>
<rect x="10" y="28" width="25" height="1" rx="0.5" fill="#D9D9D9"/>
</svg>
`;

export const tooltipicon = `
<svg width="47" height="30" viewBox="0 0 47 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="24.3711" y="10" width="4.76873" height="4.76873" rx="1" transform="rotate(45 24.3711 10)" fill="#5F9EA0"/>
<rect x="0.5" y="0.5" width="46" height="14" rx="1.5" fill="#EFEFEF" stroke="#5F9EA0"/>
<rect x="8" y="5" width="31" height="5" rx="2.5" fill="#D9D9D9"/>
<rect x="18" y="18" width="13" height="12" rx="2" fill="#5F9EA0"/>
</svg>
`;

export const bannersicon =`
<svg width="84" height="47" viewBox="0 0 84 47" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="0.85" y="0.85" width="82.0778" height="64.3" rx="0.35" fill="#EFEFEF" stroke="#5F9EA0" stroke-width="0.3"/>
<mask id="path-2-inside-1_3311_2177" fill="white">
<path d="M1 8.88867H82.7778V14.222H1V8.88867Z"/>
</mask>
<path d="M82.7778 13.722H1V14.722H82.7778V13.722Z" fill="#E4E4E4" mask="url(#path-2-inside-1_3311_2177)"/>
<circle cx="3.66623" cy="3.66623" r="0.888889" fill="#B5CFCF"/>
<circle cx="7.22287" cy="3.66623" r="0.888889" fill="#B5CFCF"/>
<circle cx="10.7776" cy="3.66623" r="0.888889" fill="#B5CFCF"/>
<circle opacity="0.4" cx="3" cy="12" r="1" fill="#5F9EA0"/>
<rect x="5" y="11.7246" width="16.5388" height="0.551292" fill="#D9D9D9"/>
<rect opacity="0.4" x="77.5898" y="11" width="4.41034" height="2" fill="#5F9EA0"/>
<rect x="78.1914" y="11.4453" width="3" height="0.5" fill="#E4E4E4"/>
<rect x="18.2715" y="17.5127" width="61.5454" height="3.23923" fill="#D9D9D9"/>
<rect opacity="0.5" x="18.2715" y="20.7529" width="61.5454" height="2.69936" fill="#D9D9D9"/>
<rect opacity="0.5" x="18.2715" y="26.6904" width="61.5454" height="2.69936" fill="#D9D9D9"/>
<rect opacity="0.5" x="18.2715" y="32.0879" width="61.5454" height="3.23923" fill="#D9D9D9"/>
<rect opacity="0.5" x="18.2715" y="38.0303" width="61.5454" height="3.23923" fill="#D9D9D9"/>
<rect opacity="0.5" x="18.2715" y="43.9688" width="61.5454" height="3.23923" fill="#D9D9D9"/>
<rect x="18.2715" y="23.4502" width="61.5454" height="3.23923" fill="#D9D9D9"/>
<rect x="18.2715" y="29.3906" width="61.5454" height="2.69936" fill="#D9D9D9"/>
<rect x="18.2715" y="35.3281" width="61.5454" height="2.69936" fill="#D9D9D9"/>
<rect x="18.2715" y="41.2656" width="61.5454" height="2.69936" fill="#D9D9D9"/>
<rect x="20.498" y="18.5928" width="4.90037" height="0.980075" fill="#E4E4E4"/>
<rect x="26.3691" y="18.5928" width="4.90037" height="0.980075" fill="#E4E4E4"/>
<rect x="32.3066" y="18.5928" width="4.90037" height="0.980075" fill="#E4E4E4"/>
<rect x="38.2461" y="18.5928" width="4.90037" height="0.980075" fill="#E4E4E4"/>
<rect x="44.1836" y="18.5928" width="4.90037" height="0.980075" fill="#E4E4E4"/>
<rect x="50.125" y="18.5928" width="4.90037" height="0.980075" fill="#E4E4E4"/>
<rect x="56.0625" y="18.5928" width="4.90037" height="0.980075" fill="#E4E4E4"/>
<rect x="62" y="18.5928" width="4.90037" height="0.980075" fill="#E4E4E4"/>
<rect x="67.9375" y="18.5928" width="4.90037" height="0.980075" fill="#E4E4E4"/>
<rect x="73.8809" y="18.5928" width="4.90037" height="0.980075" fill="#E4E4E4"/>
<rect x="1" y="14.2227" width="14.2222" height="49.7778" fill="#E4E4E4"/>
<rect x="2" y="15" width="11.7609" height="2.94022" fill="#EFEFEF"/>
<rect opacity="0.5" x="2" y="18.918" width="11.7609" height="2.94022" fill="#F0F0F0"/>
<rect opacity="0.5" x="2" y="22.8359" width="11.7609" height="2.94022" fill="#F0F0F0"/>
<rect opacity="0.5" x="2" y="26.7617" width="11.7609" height="2.94022" fill="#F0F0F0"/>
<rect opacity="0.5" x="2" y="30.6777" width="11.7609" height="2.94022" fill="#F0F0F0"/>
<rect opacity="0.4" x="2.97852" y="15.9785" width="4.90037" height="0.980075" fill="#5F9EA0"/>
<rect x="2.97852" y="19.8965" width="4.90037" height="0.980075" fill="#D9D9D9"/>
<rect x="2.97852" y="23.8203" width="4.90037" height="0.980075" fill="#D9D9D9"/>
<rect x="2.97852" y="27.7402" width="4.90037" height="0.980075" fill="#D9D9D9"/>
<rect x="2.97852" y="31.6602" width="4.90037" height="0.980075" fill="#D9D9D9"/>
</svg>
`;

export const checklisticon = `
<svg width="46" height="39" viewBox="0 0 46 39" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="0.5" y="0.5" width="45" height="38" rx="1.5" fill="#EFEFEF" stroke="#5F9EA0"/>
<rect x="4" y="5" width="36" height="3" rx="1" fill="#A7C7C8"/>
<rect x="4" y="5" width="9" height="3" rx="1" fill="#5F9EA0"/>
<path d="M4 12C4 11.4477 4.44772 11 5 11H7C7.55228 11 8 11.4477 8 12V14C8 14.5523 7.55228 15 7 15H5C4.44772 15 4 14.5523 4 14V12Z" fill="#A7C7C8"/>
<rect x="11" y="11.5" width="30" height="3" fill="#E4E4E4"/>
<path d="M4 18C4 17.4477 4.44772 17 5 17H7C7.55228 17 8 17.4477 8 18V20C8 20.5523 7.55228 21 7 21H5C4.44772 21 4 20.5523 4 20V18Z" fill="#D9D9D9"/>
<rect x="11" y="17.5" width="30" height="3" fill="#D9D9D9"/>
<path d="M4 24C4 23.4477 4.44772 23 5 23H7C7.55228 23 8 23.4477 8 24V26C8 26.5523 7.55228 27 7 27H5C4.44772 27 4 26.5523 4 26V24Z" fill="#D9D9D9"/>
<rect x="11" y="23.5" width="30" height="3" fill="#D9D9D9"/>
<path d="M4 30C4 29.4477 4.44772 29 5 29H7C7.55228 29 8 29.4477 8 30V32C8 32.5523 7.55228 33 7 33H5C4.44772 33 4 32.5523 4 32V30Z" fill="#D9D9D9"/>
<rect x="11" y="29.5" width="30" height="3" fill="#D9D9D9"/>
</svg>
`;

export const surveysicon = `
<svg width="53" height="33" viewBox="0 0 53 33" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="0.5" y="0.5" width="51.3448" height="32" rx="1.5" fill="#EFEFEF" stroke="#5F9EA0"/>
<rect opacity="0.1" x="5.68945" y="21.6211" width="5.68966" height="4.55172" fill="#5F9EA0"/>
<rect opacity="0.4" x="14.793" y="21.6211" width="5.68966" height="4.55172" fill="#5F9EA0"/>
<rect opacity="0.6" x="23.8965" y="21.6211" width="5.68966" height="4.55172" fill="#5F9EA0"/>
<rect opacity="0.8" x="33" y="21.6211" width="5.68966" height="4.55172" fill="#5F9EA0"/>
<rect x="42.1035" y="21.6211" width="5.68966" height="4.55172" fill="#5F9EA0"/>
<rect x="5.68945" y="9.10352" width="42.1034" height="3.41379" fill="#D9D9D9"/>
</svg>
`;

export const hotspotsicon = `
<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.8336 25.1377L10.8337 25.1377L11.5218 24.5383L11.6692 24.566C11.6692 24.5659 11.6692 24.5659 11.6692 24.5658C11.7422 24.1771 11.4862 23.8031 11.0975 23.73L11.0975 23.73C5.98955 22.7713 2.28173 18.3004 2.28173 13.099C2.28173 7.13461 7.13459 2.28202 13.0993 2.28202C19.0639 2.28202 23.9171 7.13461 23.9171 13.099C23.9171 18.3004 20.2093 22.7713 15.1013 23.73L15.1013 23.73C14.7124 23.8031 14.4569 24.1772 14.5296 24.5658L14.5296 24.5659C14.5941 24.9102 14.8949 25.1499 15.2325 25.1499C15.2757 25.1499 15.3204 25.146 15.3652 25.1374C21.1506 24.0518 25.3491 18.9888 25.3491 13.099C25.3491 6.3446 19.8542 0.85 13.0995 0.85C6.3449 0.85 0.85 6.3446 0.85 13.099C0.85 18.9888 5.04846 24.0518 10.8336 25.1377Z" fill="#5F9EA0" stroke="#5F9EA0" stroke-width="0.3"/>
<path d="M12.1534 20.6C12.212 20.2882 12.0066 19.9882 11.6949 19.9296L11.6949 19.9296C8.36533 19.3046 5.94828 16.3902 5.94828 12.9996C5.94828 9.11178 9.11176 5.94847 12.9998 5.94847C16.8879 5.94847 20.0515 9.11178 20.0515 12.9996C20.0515 16.3902 17.6345 19.3046 14.3049 19.9296L14.3049 19.9296C13.993 19.9882 13.7882 20.2883 13.8464 20.5998L13.8464 20.5999C13.8982 20.8762 14.1395 21.0683 14.4103 21.0683C14.4447 21.0683 14.4805 21.0652 14.5166 21.0583C18.3896 20.3315 21.2 16.9423 21.2 12.9996C21.2 8.47806 17.5217 4.8 13 4.8C8.47827 4.8 4.8 8.47806 4.8 12.9996C4.8 16.9423 7.61044 20.3315 11.4832 21.0585L11.4833 21.0585C11.7948 21.1167 12.095 20.912 12.1534 20.6ZM12.1534 20.6L11.9568 20.5631M12.1534 20.6C12.1534 20.5999 12.1534 20.5999 12.1534 20.5998L11.9568 20.5631M11.9568 20.5631C11.9188 20.7663 11.7233 20.8999 11.5201 20.8619C7.74201 20.1527 5 16.8462 5 12.9996C5 8.58853 8.58872 5 13 5C17.4113 5 21 8.58853 21 12.9996C21 16.8462 18.258 20.1527 14.4797 20.8617C14.4565 20.8662 14.4331 20.8683 14.4103 20.8683C14.2339 20.8683 14.0767 20.7429 14.043 20.5631C14.005 20.3599 14.1386 20.1643 14.3418 20.1262C17.7661 19.4834 20.2515 16.4864 20.2515 12.9996C20.2515 9.00131 16.9983 5.74847 12.9998 5.74847C9.00131 5.74847 5.74828 9.00131 5.74828 12.9996C5.74828 16.4864 8.23376 19.4834 11.658 20.1262C11.8612 20.1643 11.995 20.3599 11.9568 20.5631Z" fill="#5F9EA0" stroke="#5F9EA0" stroke-width="0.4"/>
<circle cx="13" cy="13" r="4" fill="#5F9EA0"/>
</svg>
`;

export const designicon = `
<svg width="20" height="19" viewBox="0 0 20 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.824574 7.525L4.12457 1.575C4.22457 1.39167 4.34957 1.25833 4.49957 1.175C4.64957 1.09167 4.81624 1.05 4.99957 1.05C5.18291 1.05 5.34957 1.09167 5.49957 1.175C5.64957 1.25833 5.77457 1.39167 5.87457 1.575L9.17457 7.525C9.25791 7.69167 9.29541 7.85833 9.28707 8.025C9.27874 8.19167 9.23291 8.35 9.14957 8.5C9.06624 8.65 8.94957 8.77083 8.79957 8.8625C8.64957 8.95417 8.48291 9 8.29957 9H1.69957C1.51624 9 1.34957 8.95417 1.19957 8.8625C1.04957 8.77083 0.932907 8.65 0.849574 8.5C0.766241 8.35 0.720407 8.19167 0.712074 8.025C0.703741 7.85833 0.741241 7.69167 0.824574 7.525ZM4.99957 19C3.89957 19 2.95791 18.6083 2.17457 17.825C1.39124 17.0417 0.999574 16.1 0.999574 15C0.999574 13.8833 1.39124 12.9375 2.17457 12.1625C2.95791 11.3875 3.89957 11 4.99957 11C6.09957 11 7.04124 11.3917 7.82457 12.175C8.60791 12.9583 8.99957 13.9 8.99957 15C8.99957 16.1 8.60791 17.0417 7.82457 17.825C7.04124 18.6083 6.09957 19 4.99957 19ZM4.99957 17C5.54957 17 6.02041 16.8042 6.41207 16.4125C6.80374 16.0208 6.99957 15.55 6.99957 15C6.99957 14.45 6.80374 13.9792 6.41207 13.5875C6.02041 13.1958 5.54957 13 4.99957 13C4.44957 13 3.97874 13.1958 3.58707 13.5875C3.19541 13.9792 2.99957 14.45 2.99957 15C2.99957 15.55 3.19541 16.0208 3.58707 16.4125C3.97874 16.8042 4.44957 17 4.99957 17ZM3.39957 7H6.59957L4.99957 4.125L3.39957 7ZM10.9996 18V12C10.9996 11.7167 11.0954 11.4792 11.2871 11.2875C11.4787 11.0958 11.7162 11 11.9996 11H17.9996C18.2829 11 18.5204 11.0958 18.7121 11.2875C18.9037 11.4792 18.9996 11.7167 18.9996 12V18C18.9996 18.2833 18.9037 18.5208 18.7121 18.7125C18.5204 18.9042 18.2829 19 17.9996 19H11.9996C11.7162 19 11.4787 18.9042 11.2871 18.7125C11.0954 18.5208 10.9996 18.2833 10.9996 18ZM12.9996 17H16.9996V13H12.9996V17ZM14.3496 8.45L13.0246 7.325C11.8746 6.35833 11.0829 5.59583 10.6496 5.0375C10.2162 4.47917 9.99957 3.85833 9.99957 3.175C9.99957 2.425 10.2621 1.79167 10.7871 1.275C11.3121 0.758333 11.9662 0.5 12.7496 0.5C13.1996 0.5 13.6204 0.604167 14.0121 0.8125C14.4037 1.02083 14.7329 1.30833 14.9996 1.675C15.2662 1.30833 15.5954 1.02083 15.9871 0.8125C16.3787 0.604167 16.7996 0.5 17.2496 0.5C18.0329 0.5 18.6871 0.758333 19.2121 1.275C19.7371 1.79167 19.9996 2.425 19.9996 3.175C19.9996 3.85833 19.7829 4.47917 19.3496 5.0375C18.9162 5.59583 18.1246 6.35833 16.9746 7.325L15.6496 8.45C15.4662 8.61667 15.2496 8.7 14.9996 8.7C14.7496 8.7 14.5329 8.61667 14.3496 8.45ZM14.9996 6.375C16.1996 5.375 16.9996 4.66667 17.3996 4.25C17.7996 3.83333 17.9996 3.49167 17.9996 3.225C17.9996 3.00833 17.9371 2.83333 17.8121 2.7C17.6871 2.56667 17.5162 2.5 17.2996 2.5C17.1329 2.5 16.9704 2.54583 16.8121 2.6375C16.6537 2.72917 16.4579 2.89167 16.2246 3.125L14.9996 4.3L13.7746 3.125C13.5412 2.89167 13.3454 2.72917 13.1871 2.6375C13.0287 2.54583 12.8662 2.5 12.6996 2.5C12.4829 2.5 12.3121 2.56667 12.1871 2.7C12.0621 2.83333 11.9996 3.00833 11.9996 3.225C11.9996 3.49167 12.1996 3.83333 12.5996 4.25C12.9996 4.66667 13.7996 5.375 14.9996 6.375Z" fill="#5F9D9F"/>
</svg>
`;

export const canvasicon = `
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="2" y="2" width="16" height="16" rx="2" stroke="#5F9EA0" stroke-width="1.5" fill="none"/>
<path d="M6 6L14 14M14 6L6 14" stroke="#5F9EA0" stroke-width="1" stroke-linecap="round" opacity="0.6"/>
<rect x="5" y="5" width="10" height="10" rx="1" stroke="#5F9EA0" stroke-width="0.8" fill="none" opacity="0.4"/>
</svg>
`;

export const settingsicon = `
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.82547 20C8.37547 20 7.98797 19.85 7.66297 19.55C7.33797 19.25 7.14214 18.8833 7.07547 18.45L6.85047 16.8C6.6338 16.7167 6.42964 16.6167 6.23797 16.5C6.0463 16.3833 5.8588 16.2583 5.67547 16.125L4.12547 16.775C3.7088 16.9583 3.29214 16.975 2.87547 16.825C2.4588 16.675 2.1338 16.4083 1.90047 16.025L0.725469 13.975C0.492135 13.5917 0.425469 13.1833 0.525469 12.75C0.625469 12.3167 0.850469 11.9583 1.20047 11.675L2.52547 10.675C2.5088 10.5583 2.50047 10.4458 2.50047 10.3375V9.6625C2.50047 9.55417 2.5088 9.44167 2.52547 9.325L1.20047 8.325C0.850469 8.04167 0.625469 7.68333 0.525469 7.25C0.425469 6.81667 0.492135 6.40833 0.725469 6.025L1.90047 3.975C2.1338 3.59167 2.4588 3.325 2.87547 3.175C3.29214 3.025 3.7088 3.04167 4.12547 3.225L5.67547 3.875C5.8588 3.74167 6.05047 3.61667 6.25047 3.5C6.45047 3.38333 6.65047 3.28333 6.85047 3.2L7.07547 1.55C7.14214 1.11667 7.33797 0.75 7.66297 0.45C7.98797 0.15 8.37547 0 8.82547 0H11.1755C11.6255 0 12.013 0.15 12.338 0.45C12.663 0.75 12.8588 1.11667 12.9255 1.55L13.1505 3.2C13.3671 3.28333 13.5713 3.38333 13.763 3.5C13.9546 3.61667 14.1421 3.74167 14.3255 3.875L15.8755 3.225C16.2921 3.04167 16.7088 3.025 17.1255 3.175C17.5421 3.325 17.8671 3.59167 18.1005 3.975L19.2755 6.025C19.5088 6.40833 19.5755 6.81667 19.4755 7.25C19.3755 7.68333 19.1505 8.04167 18.8005 8.325L17.4755 9.325C17.4921 9.44167 17.5005 9.55417 17.5005 9.6625V10.3375C17.5005 10.4458 17.4838 10.5583 17.4505 10.675L18.7755 11.675C19.1255 11.9583 19.3505 12.3167 19.4505 12.75C19.5505 13.1833 19.4838 13.5917 19.2505 13.975L18.0505 16.025C17.8171 16.4083 17.4921 16.675 17.0755 16.825C16.6588 16.975 16.2421 16.9583 15.8255 16.775L14.3255 16.125C14.1421 16.2583 13.9505 16.3833 13.7505 16.5C13.5505 16.6167 13.3505 16.7167 13.1505 16.8L12.9255 18.45C12.8588 18.8833 12.663 19.25 12.338 19.55C12.013 19.85 11.6255 20 11.1755 20H8.82547ZM9.00047 18H10.9755L11.3255 15.35C11.8421 15.2167 12.3213 15.0208 12.763 14.7625C13.2046 14.5042 13.6088 14.1917 13.9755 13.825L16.4505 14.85L17.4255 13.15L15.2755 11.525C15.3588 11.2917 15.4171 11.0458 15.4505 10.7875C15.4838 10.5292 15.5005 10.2667 15.5005 10C15.5005 9.73333 15.4838 9.47083 15.4505 9.2125C15.4171 8.95417 15.3588 8.70833 15.2755 8.475L17.4255 6.85L16.4505 5.15L13.9755 6.2C13.6088 5.81667 13.2046 5.49583 12.763 5.2375C12.3213 4.97917 11.8421 4.78333 11.3255 4.65L11.0005 2H9.02547L8.67547 4.65C8.1588 4.78333 7.67964 4.97917 7.23797 5.2375C6.7963 5.49583 6.39214 5.80833 6.02547 6.175L3.55047 5.15L2.57547 6.85L4.72547 8.45C4.64214 8.7 4.5838 8.95 4.55047 9.2C4.51714 9.45 4.50047 9.71667 4.50047 10C4.50047 10.2667 4.51714 10.525 4.55047 10.775C4.5838 11.025 4.64214 11.275 4.72547 11.525L2.57547 13.15L3.55047 14.85L6.02547 13.8C6.39214 14.1833 6.7963 14.5042 7.23797 14.7625C7.67964 15.0208 8.1588 15.2167 8.67547 15.35L9.00047 18ZM10.0505 13.5C11.0171 13.5 11.8421 13.1583 12.5255 12.475C13.2088 11.7917 13.5505 10.9667 13.5505 10C13.5505 9.03333 13.2088 8.20833 12.5255 7.525C11.8421 6.84167 11.0171 6.5 10.0505 6.5C9.06713 6.5 8.23797 6.84167 7.56297 7.525C6.88797 8.20833 6.55047 9.03333 6.55047 10C6.55047 10.9667 6.88797 11.7917 7.56297 12.475C8.23797 13.1583 9.06713 13.5 10.0505 13.5Z" fill="#5F9D9F"/>
</svg>
`;

export const undoicon = `
<svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4.00078 14.9998C3.71745 14.9998 3.47995 14.904 3.28828 14.7123C3.09661 14.5206 3.00078 14.2831 3.00078 13.9998C3.00078 13.7165 3.09661 13.479 3.28828 13.2873C3.47995 13.0956 3.71745 12.9998 4.00078 12.9998H10.1008C11.1508 12.9998 12.0633 12.6665 12.8383 11.9998C13.6133 11.3331 14.0008 10.4998 14.0008 9.4998C14.0008 8.4998 13.6133 7.66647 12.8383 6.9998C12.0633 6.33314 11.1508 5.9998 10.1008 5.9998H3.80078L5.70078 7.8998C5.88411 8.08314 5.97578 8.31647 5.97578 8.5998C5.97578 8.88314 5.88411 9.11647 5.70078 9.2998C5.51745 9.48314 5.28411 9.57481 5.00078 9.57481C4.71745 9.57481 4.48411 9.48314 4.30078 9.2998L0.700781 5.6998C0.600781 5.5998 0.529948 5.49147 0.488281 5.3748C0.446615 5.25814 0.425781 5.13314 0.425781 4.9998C0.425781 4.86647 0.446615 4.74147 0.488281 4.6248C0.529948 4.50814 0.600781 4.3998 0.700781 4.2998L4.30078 0.699805C4.48411 0.516471 4.71745 0.424805 5.00078 0.424805C5.28411 0.424805 5.51745 0.516471 5.70078 0.699805C5.88411 0.883138 5.97578 1.11647 5.97578 1.3998C5.97578 1.68314 5.88411 1.91647 5.70078 2.0998L3.80078 3.9998H10.1008C11.7174 3.9998 13.1049 4.5248 14.2633 5.5748C15.4216 6.6248 16.0008 7.93314 16.0008 9.4998C16.0008 11.0665 15.4216 12.3748 14.2633 13.4248C13.1049 14.4748 11.7174 14.9998 10.1008 14.9998H4.00078Z" fill="#5F9D9F"/>
</svg>
`;

export const redoicon = `
<svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.2 5.9998H5.9C4.85 5.9998 3.9375 6.33314 3.1625 6.9998C2.3875 7.66647 2 8.4998 2 9.4998C2 10.4998 2.3875 11.3331 3.1625 11.9998C3.9375 12.6665 4.85 12.9998 5.9 12.9998H12C12.2833 12.9998 12.5208 13.0956 12.7125 13.2873C12.9042 13.479 13 13.7165 13 13.9998C13 14.2831 12.9042 14.5206 12.7125 14.7123C12.5208 14.904 12.2833 14.9998 12 14.9998H5.9C4.28333 14.9998 2.89583 14.4748 1.7375 13.4248C0.579167 12.3748 0 11.0665 0 9.4998C0 7.93314 0.579167 6.6248 1.7375 5.5748C2.89583 4.5248 4.28333 3.9998 5.9 3.9998H12.2L10.3 2.0998C10.1167 1.91647 10.025 1.68314 10.025 1.3998C10.025 1.11647 10.1167 0.883138 10.3 0.699805C10.4833 0.516471 10.7167 0.424805 11 0.424805C11.2833 0.424805 11.5167 0.516471 11.7 0.699805L15.3 4.2998C15.4 4.3998 15.4708 4.50814 15.5125 4.6248C15.5542 4.74147 15.575 4.86647 15.575 4.9998C15.575 5.13314 15.5542 5.25814 15.5125 5.3748C15.4708 5.49147 15.4 5.5998 15.3 5.6998L11.7 9.2998C11.5167 9.48314 11.2833 9.57481 11 9.57481C10.7167 9.57481 10.4833 9.48314 10.3 9.2998C10.1167 9.11647 10.025 8.88314 10.025 8.5998C10.025 8.31647 10.1167 8.08314 10.3 7.8998L12.2 5.9998Z" fill="#5F9D9F"/>
</svg>
`;

export const shareicon = `
<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15 20C14.1667 20 13.4583 19.7083 12.875 19.125C12.2917 18.5417 12 17.8333 12 17C12 16.9 12.025 16.6667 12.075 16.3L5.05 12.2C4.78333 12.45 4.475 12.6458 4.125 12.7875C3.775 12.9292 3.4 13 3 13C2.16667 13 1.45833 12.7083 0.875 12.125C0.291667 11.5417 0 10.8333 0 10C0 9.16667 0.291667 8.45833 0.875 7.875C1.45833 7.29167 2.16667 7 3 7C3.4 7 3.775 7.07083 4.125 7.2125C4.475 7.35417 4.78333 7.55 5.05 7.8L12.075 3.7C12.0417 3.58333 12.0208 3.47083 12.0125 3.3625C12.0042 3.25417 12 3.13333 12 3C12 2.16667 12.2917 1.45833 12.875 0.875C13.4583 0.291667 14.1667 0 15 0C15.8333 0 16.5417 0.291667 17.125 0.875C17.7083 1.45833 18 2.16667 18 3C18 3.83333 17.7083 4.54167 17.125 5.125C16.5417 5.70833 15.8333 6 15 6C14.6 6 14.225 5.92917 13.875 5.7875C13.525 5.64583 13.2167 5.45 12.95 5.2L5.925 9.3C5.95833 9.41667 5.97917 9.52917 5.9875 9.6375C5.99583 9.74583 6 9.86667 6 10C6 10.1333 5.99583 10.2542 5.9875 10.3625C5.97917 10.4708 5.95833 10.5833 5.925 10.7L12.95 14.8C13.2167 14.55 13.525 14.3542 13.875 14.2125C14.225 14.0708 14.6 14 15 14C15.8333 14 16.5417 14.2917 17.125 14.875C17.7083 15.4583 18 16.1667 18 17C18 17.8333 17.7083 18.5417 17.125 19.125C16.5417 19.7083 15.8333 20 15 20ZM15 18C15.2833 18 15.5208 17.9042 15.7125 17.7125C15.9042 17.5208 16 17.2833 16 17C16 16.7167 15.9042 16.4792 15.7125 16.2875C15.5208 16.0958 15.2833 16 15 16C14.7167 16 14.4792 16.0958 14.2875 16.2875C14.0958 16.4792 14 16.7167 14 17C14 17.2833 14.0958 17.5208 14.2875 17.7125C14.4792 17.9042 14.7167 18 15 18ZM3 11C3.28333 11 3.52083 10.9042 3.7125 10.7125C3.90417 10.5208 4 10.2833 4 10C4 9.71667 3.90417 9.47917 3.7125 9.2875C3.52083 9.09583 3.28333 9 3 9C2.71667 9 2.47917 9.09583 2.2875 9.2875C2.09583 9.47917 2 9.71667 2 10C2 10.2833 2.09583 10.5208 2.2875 10.7125C2.47917 10.9042 2.71667 11 3 11ZM15 4C15.2833 4 15.5208 3.90417 15.7125 3.7125C15.9042 3.52083 16 3.28333 16 3C16 2.71667 15.9042 2.47917 15.7125 2.2875C15.5208 2.09583 15.2833 2 15 2C14.7167 2 14.4792 2.09583 14.2875 2.2875C14.0958 2.47917 14 2.71667 14 3C14 3.28333 14.0958 3.52083 14.2875 3.7125C14.4792 3.90417 14.7167 4 15 4Z" fill="#5F9EA0"/>
</svg>
`;

export const playicon = `
<svg width="11" height="14" viewBox="0 0 11 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 12.1752V1.8252C0 1.54186 0.1 1.30436 0.3 1.1127C0.5 0.921029 0.733333 0.825195 1 0.825195C1.08333 0.825195 1.17083 0.837695 1.2625 0.862695C1.35417 0.887695 1.44167 0.925195 1.525 0.975195L9.675 6.1502C9.825 6.2502 9.9375 6.3752 10.0125 6.5252C10.0875 6.6752 10.125 6.83353 10.125 7.0002C10.125 7.16686 10.0875 7.3252 10.0125 7.4752C9.9375 7.6252 9.825 7.7502 9.675 7.8502L1.525 13.0252C1.44167 13.0752 1.35417 13.1127 1.2625 13.1377C1.17083 13.1627 1.08333 13.1752 1 13.1752C0.733333 13.1752 0.5 13.0794 0.3 12.8877C0.1 12.696 0 12.4585 0 12.1752ZM2 10.3502L7.25 7.0002L2 3.6502V10.3502Z" fill="#5F9D9F"/>
</svg>
`;


export const uploadimage = `
<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7 16V7.85L4.4 10.45L3 9L8 4L13 9L11.6 10.45L9 7.85V16H7ZM0 5V2C0 1.45 0.195833 0.979167 0.5875 0.5875C0.979167 0.195833 1.45 0 2 0H14C14.55 0 15.0208 0.195833 15.4125 0.5875C15.8042 0.979167 16 1.45 16 2V5H14V2H2V5H0Z" fill="#1C1B1F"/>
</svg>
`;

export const copy = `
<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.0996094 16.5V14.5H2.09961V16.5H0.0996094ZM0.0996094 13V11H2.09961V13H0.0996094ZM0.0996094 9.5V7.5H2.09961V9.5H0.0996094ZM3.59961 20V18H5.59961V20H3.59961ZM6.09961 16C5.54961 16 5.07878 15.8042 4.68711 15.4125C4.29544 15.0208 4.09961 14.55 4.09961 14V2C4.09961 1.45 4.29544 0.979167 4.68711 0.5875C5.07878 0.195833 5.54961 0 6.09961 0H15.0996C15.6496 0 16.1204 0.195833 16.5121 0.5875C16.9038 0.979167 17.0996 1.45 17.0996 2V14C17.0996 14.55 16.9038 15.0208 16.5121 15.4125C16.1204 15.8042 15.6496 16 15.0996 16H6.09961ZM6.09961 14H15.0996V2H6.09961V14ZM7.09961 20V18H9.09961V20H7.09961ZM2.09961 20C1.54961 20 1.07878 19.8042 0.687109 19.4125C0.295443 19.0208 0.0996094 18.55 0.0996094 18H2.09961V20ZM10.5996 20V18H12.5996C12.5996 18.55 12.4038 19.0208 12.0121 19.4125C11.6204 19.8042 11.1496 20 10.5996 20ZM0.0996094 6C0.0996094 5.45 0.295443 4.97917 0.687109 4.5875C1.07878 4.19583 1.54961 4 2.09961 4V6H0.0996094Z" fill="#1C1B1F"/>
</svg>
`;

export const trash = `
<svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.09961 18C2.54961 18 2.07878 17.8042 1.68711 17.4125C1.29544 17.0208 1.09961 16.55 1.09961 16V3H0.0996094V1H5.09961V0H11.0996V1H16.0996V3H15.0996V16C15.0996 16.55 14.9038 17.0208 14.5121 17.4125C14.1204 17.8042 13.6496 18 13.0996 18H3.09961ZM13.0996 3H3.09961V16H13.0996V3ZM5.09961 14H7.09961V5H5.09961V14ZM9.09961 14H11.0996V5H9.09961V14Z" fill="#1C1B1F"/>
</svg>
`;


export const uploadfile = `
<svg width="22" height="17" viewBox="0 0 22 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5.5 16.5C3.98333 16.5 2.6875 15.975 1.6125 14.925C0.5375 13.875 0 12.5917 0 11.075C0 9.775 0.391667 8.61667 1.175 7.6C1.95833 6.58333 2.98333 5.93333 4.25 5.65C4.66667 4.11667 5.5 2.875 6.75 1.925C8 0.975 9.41667 0.5 11 0.5C12.95 0.5 14.6042 1.17917 15.9625 2.5375C17.3208 3.89583 18 5.55 18 7.5C19.15 7.63333 20.1042 8.12917 20.8625 8.9875C21.6208 9.84583 22 10.85 22 12C22 13.25 21.5625 14.3125 20.6875 15.1875C19.8125 16.0625 18.75 16.5 17.5 16.5H12C11.45 16.5 10.9792 16.3042 10.5875 15.9125C10.1958 15.5208 10 15.05 10 14.5V9.35L8.4 10.9L7 9.5L11 5.5L15 9.5L13.6 10.9L12 9.35V14.5H17.5C18.2 14.5 18.7917 14.2583 19.275 13.775C19.7583 13.2917 20 12.7 20 12C20 11.3 19.7583 10.7083 19.275 10.225C18.7917 9.74167 18.2 9.5 17.5 9.5H16V7.5C16 6.11667 15.5125 4.9375 14.5375 3.9625C13.5625 2.9875 12.3833 2.5 11 2.5C9.61667 2.5 8.4375 2.9875 7.4625 3.9625C6.4875 4.9375 6 6.11667 6 7.5H5.5C4.53333 7.5 3.70833 7.84167 3.025 8.525C2.34167 9.20833 2 10.0333 2 11C2 11.9667 2.34167 12.7917 3.025 13.475C3.70833 14.1583 4.53333 14.5 5.5 14.5H8V16.5H5.5Z" fill="#1C1B1F"/>
</svg>
`;

export const hyperlink =`
<svg width="45" height="45" viewBox="0 0 45 45" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1" y="1" width="43" height="43" rx="14.5" fill="white"/>
<rect x="1" y="1" width="43" height="43" rx="14.5" stroke="#5F9EA0"/>
<path d="M21.5 27.5H17.5C16.1167 27.5 14.9375 27.0125 13.9625 26.0375C12.9875 25.0625 12.5 23.8833 12.5 22.5C12.5 21.1167 12.9875 19.9375 13.9625 18.9625C14.9375 17.9875 16.1167 17.5 17.5 17.5H21.5V19.5H17.5C16.6667 19.5 15.9583 19.7917 15.375 20.375C14.7917 20.9583 14.5 21.6667 14.5 22.5C14.5 23.3333 14.7917 24.0417 15.375 24.625C15.9583 25.2083 16.6667 25.5 17.5 25.5H21.5V27.5ZM18.5 23.5V21.5H26.5V23.5H18.5ZM23.5 27.5V25.5H27.5C28.3333 25.5 29.0417 25.2083 29.625 24.625C30.2083 24.0417 30.5 23.3333 30.5 22.5C30.5 21.6667 30.2083 20.9583 29.625 20.375C29.0417 19.7917 28.3333 19.5 27.5 19.5H23.5V17.5H27.5C28.8833 17.5 30.0625 17.9875 31.0375 18.9625C32.0125 19.9375 32.5 21.1167 32.5 22.5C32.5 23.8833 32.0125 25.0625 31.0375 26.0375C30.0625 27.0125 28.8833 27.5 27.5 27.5H23.5Z" fill="#5F9EA0"/>
</svg>
`;

export const files = `
<svg width="49" height="45" viewBox="0 0 49 45" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1" y="1" width="47" height="43" rx="14.5" fill="white"/>
<rect x="1" y="1" width="47" height="43" rx="14.5" stroke="#5F9EA0"/>
<mask id="mask0_2730_61115" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="12" y="10" width="25" height="25">
<rect x="12.5" y="10.5" width="24" height="24" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_2730_61115)">
<path d="M15.5 31.5C14.95 31.5 14.4792 31.3042 14.0875 30.9125C13.6958 30.5208 13.5 30.05 13.5 29.5V16.5H15.5V29.5H32.5V31.5H15.5ZM19.5 27.5C18.95 27.5 18.4792 27.3042 18.0875 26.9125C17.6958 26.5208 17.5 26.05 17.5 25.5V14.5C17.5 13.95 17.6958 13.4792 18.0875 13.0875C18.4792 12.6958 18.95 12.5 19.5 12.5H24.5L26.5 14.5H33.5C34.05 14.5 34.5208 14.6958 34.9125 15.0875C35.3042 15.4792 35.5 15.95 35.5 16.5V25.5C35.5 26.05 35.3042 26.5208 34.9125 26.9125C34.5208 27.3042 34.05 27.5 33.5 27.5H19.5ZM19.5 25.5H33.5V16.5H25.675L23.675 14.5H19.5V25.5Z" fill="#5F9EA0"/>
</g>
</svg>
`

export const uploadicon = `
<svg width="41" height="45" viewBox="0 0 41 45" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1" y="1" width="39" height="43" rx="14.5" fill="white"/>
<rect x="1" y="1" width="39" height="43" rx="14.5" stroke="#5F9EA0"/>
<path d="M19.5 30.5V22.35L16.9 24.95L15.5 23.5L20.5 18.5L25.5 23.5L24.1 24.95L21.5 22.35V30.5H19.5ZM12.5 19.5V16.5C12.5 15.95 12.6958 15.4792 13.0875 15.0875C13.4792 14.6958 13.95 14.5 14.5 14.5H26.5C27.05 14.5 27.5208 14.6958 27.9125 15.0875C28.3042 15.4792 28.5 15.95 28.5 16.5V19.5H26.5V16.5H14.5V19.5H12.5Z" fill="#5F9EA0"/>
</svg>
`;

export const replaceimageicon = `
<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7 16V7.85L4.4 10.45L3 9L8 4L13 9L11.6 10.45L9 7.85V16H7ZM0 5V2C0 1.45 0.195833 0.979167 0.5875 0.5875C0.979167 0.195833 1.45 0 2 0H14C14.55 0 15.0208 0.195833 15.4125 0.5875C15.8042 0.979167 16 1.45 16 2V5H14V2H2V5H0Z" fill="#1C1B1F"/>
</svg>
`;

export const galleryicon = `
<svg width="23" height="14" viewBox="0 0 23 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.5 14C1.95 14 1.47917 13.8042 1.0875 13.4125C0.695833 13.0208 0.5 12.55 0.5 12V2C0.5 1.45 0.695833 0.979167 1.0875 0.5875C1.47917 0.195833 1.95 0 2.5 0H12.5C13.05 0 13.5208 0.195833 13.9125 0.5875C14.3042 0.979167 14.5 1.45 14.5 2V12C14.5 12.55 14.3042 13.0208 13.9125 13.4125C13.5208 13.8042 13.05 14 12.5 14H2.5ZM17.5 6C17.2167 6 16.9792 5.90417 16.7875 5.7125C16.5958 5.52083 16.5 5.28333 16.5 5V1C16.5 0.716667 16.5958 0.479167 16.7875 0.2875C16.9792 0.0958333 17.2167 0 17.5 0H21.5C21.7833 0 22.0208 0.0958333 22.2125 0.2875C22.4042 0.479167 22.5 0.716667 22.5 1V5C22.5 5.28333 22.4042 5.52083 22.2125 5.7125C22.0208 5.90417 21.7833 6 21.5 6H17.5ZM18.5 4H20.5V2H18.5V4ZM2.5 12H12.5V2H2.5V12ZM3.5 10H11.5L8.875 6.5L7 9L5.625 7.175L3.5 10ZM17.5 14C17.2167 14 16.9792 13.9042 16.7875 13.7125C16.5958 13.5208 16.5 13.2833 16.5 13V9C16.5 8.71667 16.5958 8.47917 16.7875 8.2875C16.9792 8.09583 17.2167 8 17.5 8H21.5C21.7833 8 22.0208 8.09583 22.2125 8.2875C22.4042 8.47917 22.5 8.71667 22.5 9V13C22.5 13.2833 22.4042 13.5208 22.2125 13.7125C22.0208 13.9042 21.7833 14 21.5 14H17.5ZM18.5 12H20.5V10H18.5V12Z" fill="#1C1B1F"/>
</svg>
`;

export const sectionheight =
`
<svg width="7" height="20" viewBox="0 0 7 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.317958 18.6364L2.96946 18.6364L0.317959 15.9091L2.59069 15.9091L2.59069 4.09091L0.317959 4.09091L2.96951 1.36364L0.318005 1.36364L0.318005 -2.78163e-07L6.68164 0L6.68164 1.36364L4.03009 1.36364L6.6816 4.09091L4.40887 4.09091L4.40887 15.9091L6.68159 15.9091L4.03005 18.6364L6.68159 18.6364L6.68159 20L0.317958 20L0.317958 18.6364Z" fill="black"/>
</svg>

`;

export const backgroundcoloricon =
`
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10 19C8.74682 19 7.58226 18.763 6.49619 18.2943C5.39072 17.8173 4.43908 17.1749 3.63211 16.3679C2.82514 15.5609 2.18269 14.6093 1.70567 13.5038C1.23702 12.4177 1 11.2532 1 10C1 8.74682 1.23702 7.58226 1.70567 6.49619C2.18269 5.39072 2.82514 4.43908 3.63211 3.63211C4.43908 2.82514 5.39072 2.18269 6.49619 1.70567C7.58226 1.23702 8.74682 1 10 1C11.2532 1 12.4177 1.23702 13.5038 1.70567C14.6093 2.18269 15.5609 2.82514 16.3679 3.63211C17.1749 4.43908 17.8173 5.39072 18.2943 6.49619C18.763 7.58226 19 8.74682 19 10C19 11.2532 18.763 12.4177 18.2943 13.5038C17.8173 14.6093 17.1749 15.5609 16.3679 16.3679C15.5609 17.1749 14.6093 17.8173 13.5038 18.2943C12.4177 18.763 11.2532 19 10 19Z" fill="white" stroke="black" stroke-width="2"/>
</svg>

`;

export const copyicon = 
`
<svg width="17" height="20" viewBox="0 0 17 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 16.5V14.5H2V16.5H0ZM0 13V11H2V13H0ZM0 9.5V7.5H2V9.5H0ZM3.5 20V18H5.5V20H3.5ZM6 16C5.45 16 4.97917 15.8042 4.5875 15.4125C4.19583 15.0208 4 14.55 4 14V2C4 1.45 4.19583 0.979167 4.5875 0.5875C4.97917 0.195833 5.45 0 6 0H15C15.55 0 16.0208 0.195833 16.4125 0.5875C16.8042 0.979167 17 1.45 17 2V14C17 14.55 16.8042 15.0208 16.4125 15.4125C16.0208 15.8042 15.55 16 15 16H6ZM6 14H15V2H6V14ZM7 20V18H9V20H7ZM2 20C1.45 20 0.979167 19.8042 0.5875 19.4125C0.195833 19.0208 0 18.55 0 18H2V20ZM10.5 20V18H12.5C12.5 18.55 12.3042 19.0208 11.9125 19.4125C11.5208 19.8042 11.05 20 10.5 20ZM0 6C0 5.45 0.195833 4.97917 0.5875 4.5875C0.979167 4.19583 1.45 4 2 4V6H0Z" fill="#1C1B1F"/>
</svg>

`;

export const deleteicon = 
`
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_2730_61099" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
<rect width="24" height="24" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_2730_61099)">
<path d="M7 21C6.45 21 5.97917 20.8042 5.5875 20.4125C5.19583 20.0208 5 19.55 5 19V6H4V4H9V3H15V4H20V6H19V19C19 19.55 18.8042 20.0208 18.4125 20.4125C18.0208 20.8042 17.55 21 17 21H7ZM17 6H7V19H17V6ZM9 17H11V8H9V17ZM13 17H15V8H13V17Z" fill="#1C1B1F"/>
</g>
</svg>

`;


export const bold = `
<svg width="11" height="14" viewBox="0 0 11 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 14V0H5.525C6.60833 0 7.60833 0.333333 8.525 1C9.44167 1.66667 9.9 2.59167 9.9 3.775C9.9 4.625 9.70833 5.27917 9.325 5.7375C8.94167 6.19583 8.58333 6.525 8.25 6.725C8.66667 6.90833 9.12917 7.25 9.6375 7.75C10.1458 8.25 10.4 9 10.4 10C10.4 11.4833 9.85833 12.5208 8.775 13.1125C7.69167 13.7042 6.675 14 5.725 14H0ZM3.025 11.2H5.625C6.425 11.2 6.9125 10.9958 7.0875 10.5875C7.2625 10.1792 7.35 9.88333 7.35 9.7C7.35 9.51667 7.2625 9.22083 7.0875 8.8125C6.9125 8.40417 6.4 8.2 5.55 8.2H3.025V11.2ZM3.025 5.5H5.35C5.9 5.5 6.3 5.35833 6.55 5.075C6.8 4.79167 6.925 4.475 6.925 4.125C6.925 3.725 6.78333 3.4 6.5 3.15C6.21667 2.9 5.85 2.775 5.4 2.775H3.025V5.5Z" fill="#1C1B1F"/>
</svg>
`;

export const italic = `
<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.399902 14V11.5H4.3999L7.3999 2.5H3.3999V0H13.3999V2.5H9.8999L6.8999 11.5H10.3999V14H0.399902Z" fill="#1C1B1F"/>
</svg>
`;

export const underline = `  
<svg width="15" height="18" viewBox="0 0 15 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.899902 17.5V16.5H13.8999V17.5H0.899902ZM7.3999 13.5C5.82849 13.5 4.6683 13.015 3.84861 12.0928C3.01449 11.1544 2.5749 9.89072 2.5749 8.25V0.5H4.1499V8.4C4.1499 9.41789 4.40568 10.2951 4.95947 10.9873C5.54067 11.7138 6.38253 12.05 7.3999 12.05C8.41728 12.05 9.25914 11.7139 9.84034 10.9873C10.3941 10.2951 10.6499 9.41789 10.6499 8.4V0.5H12.2249V8.25C12.2249 9.89072 11.7853 11.1544 10.9512 12.0928C10.1315 13.015 8.97132 13.5 7.3999 13.5Z" fill="#1C1B1F" stroke="black"/>
</svg>
`;

export const strikeout = `
<svg width="21" height="17" viewBox="0 0 21 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.5499 17.0001C9.28324 17.0001 8.15824 16.6251 7.1749 15.8751C6.19157 15.1251 5.48324 14.1001 5.0499 12.8001L7.2499 11.8501C7.48324 12.6501 7.8874 13.3084 8.4624 13.8251C9.0374 14.3418 9.7499 14.6001 10.5999 14.6001C11.2999 14.6001 11.9332 14.4334 12.4999 14.1001C13.0666 13.7668 13.3499 13.2334 13.3499 12.5001C13.3499 12.2001 13.2916 11.9251 13.1749 11.6751C13.0582 11.4251 12.8999 11.2001 12.6999 11.0001H15.4999C15.5832 11.2334 15.6457 11.4709 15.6874 11.7126C15.7291 11.9543 15.7499 12.2168 15.7499 12.5001C15.7499 13.9334 15.2374 15.0418 14.2124 15.8251C13.1874 16.6084 11.9666 17.0001 10.5499 17.0001ZM0.399902 9.0001V7.0001H20.3999V9.0001H0.399902ZM10.4499 0.850098C11.5499 0.850098 12.5124 1.12093 13.3374 1.6626C14.1624 2.20426 14.7999 3.03343 15.2499 4.1501L13.0499 5.1251C12.8999 4.64176 12.6207 4.20843 12.2124 3.8251C11.8041 3.44176 11.2332 3.2501 10.4999 3.2501C9.81657 3.2501 9.2499 3.40426 8.7999 3.7126C8.3499 4.02093 8.0999 4.4501 8.0499 5.0001H5.6499C5.68324 3.8501 6.1374 2.87093 7.0124 2.0626C7.8874 1.25426 9.03324 0.850098 10.4499 0.850098Z" fill="#1C1B1F"/>
</svg>
`;

export const sketch = `
<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 21V17H20V21H0ZM3.5 14L8.75 0H11.25L16.5 14H14.1L12.85 10.4H7.2L5.9 14H3.5ZM7.9 8.4H12.1L10.05 2.6H9.95L7.9 8.4Z" fill="#1C1B1F"/>
</svg>
`;

export const paint = `
<svg width="21" height="24" viewBox="0 0 21 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4.82197 1.4L6.24697 0L14.822 8.575C15.2053 8.95833 15.397 9.43333 15.397 10C15.397 10.5667 15.2053 11.0417 14.822 11.425L10.072 16.175C9.68864 16.5583 9.21364 16.75 8.64697 16.75C8.08031 16.75 7.60531 16.5583 7.22197 16.175L2.47197 11.425C2.08864 11.0417 1.89697 10.5667 1.89697 10C1.89697 9.43333 2.08864 8.95833 2.47197 8.575L7.22197 3.8L4.82197 1.4ZM8.64697 5.225L3.87197 10H13.422L8.64697 5.225ZM17.647 17C17.097 17 16.6261 16.8042 16.2345 16.4125C15.8428 16.0208 15.647 15.55 15.647 15C15.647 14.65 15.7511 14.275 15.9595 13.875C16.1678 13.475 16.397 13.1 16.647 12.75C16.797 12.55 16.9553 12.3417 17.122 12.125C17.2886 11.9083 17.4636 11.7 17.647 11.5C17.8303 11.7 18.0053 11.9083 18.172 12.125C18.3386 12.3417 18.497 12.55 18.647 12.75C18.897 13.1 19.1261 13.475 19.3345 13.875C19.5428 14.275 19.647 14.65 19.647 15C19.647 15.55 19.4511 16.0208 19.0595 16.4125C18.6678 16.8042 18.197 17 17.647 17ZM0.646973 24V20H20.647V24H0.646973Z" fill="#1C1B1F"/>
</svg>
`;



export const image = `
<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.88135 18C2.33135 18 1.86051 17.8042 1.46885 17.4125C1.07718 17.0208 0.881348 16.55 0.881348 16V2C0.881348 1.45 1.07718 0.979167 1.46885 0.5875C1.86051 0.195833 2.33135 0 2.88135 0H16.8813C17.4313 0 17.9022 0.195833 18.2938 0.5875C18.6855 0.979167 18.8813 1.45 18.8813 2V16C18.8813 16.55 18.6855 17.0208 18.2938 17.4125C17.9022 17.8042 17.4313 18 16.8813 18H2.88135ZM2.88135 16H16.8813V2H2.88135V16ZM3.88135 14H15.8813L12.1313 9L9.13135 13L6.88135 10L3.88135 14ZM6.38135 7C6.79801 7 7.15218 6.85417 7.44385 6.5625C7.73551 6.27083 7.88135 5.91667 7.88135 5.5C7.88135 5.08333 7.73551 4.72917 7.44385 4.4375C7.15218 4.14583 6.79801 4 6.38135 4C5.96468 4 5.61051 4.14583 5.31885 4.4375C5.02718 4.72917 4.88135 5.08333 4.88135 5.5C4.88135 5.91667 5.02718 6.27083 5.31885 6.5625C5.61051 6.85417 5.96468 7 6.38135 7Z" fill="#1C1B1F"/>
</svg>
`

export const ProductToursicon = `
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.29297 12C9.8703 12 11.9596 9.91068 11.9596 7.33335C11.9596 4.75602 9.8703 2.66669 7.29297 2.66669C4.71564 2.66669 2.6263 4.75602 2.6263 7.33335C2.6263 9.91068 4.71564 12 7.29297 12Z" stroke="#5F9EA0" stroke-width="2"/>
<path d="M22.6263 20H26.6263C28.093 20 29.293 21.2 29.293 22.6667V26.6667C29.293 28.1333 28.093 29.3333 26.6263 29.3333H22.6263C21.1596 29.3333 19.9596 28.1333 19.9596 26.6667V22.6667C19.9596 21.2 21.1596 20 22.6263 20Z" stroke="#5F9EA0" stroke-width="2"/>
<path d="M15.9996 6.66669H19.5729C22.0396 6.66669 23.1863 9.72002 21.3329 11.3467L10.6796 20.6667C8.82627 22.28 9.97293 25.3334 12.4263 25.3334H15.9996" stroke="#5F9EA0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.3143 7.33335H7.32971" stroke="#5F9EA0" stroke-width="2.66667" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M24.6476 24.6667H24.663" stroke="#5F9EA0" stroke-width="2.66667" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

`

export const Tooltipsicon = `
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.3333 25.3334H10.6667C5.33333 25.3334 2.66667 24 2.66667 17.3334V10.6667C2.66667 5.33335 5.33333 2.66669 10.6667 2.66669H21.3333C26.6667 2.66669 29.3333 5.33335 29.3333 10.6667V17.3334C29.3333 22.6667 26.6667 25.3334 21.3333 25.3334H20.6667C20.2533 25.3334 19.8533 25.5334 19.6 25.8667L17.6 28.5334C16.72 29.7067 15.28 29.7067 14.4 28.5334L12.4 25.8667C12.1867 25.5734 11.6933 25.3334 11.3333 25.3334Z" stroke="#5F9EA0" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.33333 10.6667H22.6667" stroke="#5F9EA0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.33333 17.3333H17.3333" stroke="#5F9EA0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`
export const Announcementsicon = `
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M22.2122 16.459L23.1043 19.7887C23.3716 20.7899 23.0815 21.8582 22.3417 22.5848C21.6031 23.3114 20.53 23.5848 19.5336 23.2994L12.5469 21.3007C12.4726 21.2803 12.3959 21.2683 12.3203 21.2659L13.5145 23.4169C13.5481 23.4781 13.5889 23.5356 13.6356 23.5872L14.2819 24.3174C14.9437 25.0632 15.1716 26.0991 14.8862 27.0547C14.5985 28.0091 13.8371 28.7477 12.8743 29.0067L11.9487 29.2537C10.547 29.6302 9.08545 28.9024 8.53881 27.5583L6.48492 22.5082C4.96698 22.3331 3.6361 21.254 3.21657 19.6881L2.22378 15.982C1.67463 13.9353 2.88924 11.8312 4.93592 11.283L9.05574 10.1787C9.2236 10.1344 9.37588 10.0444 9.49697 9.91853L14.5484 4.69556C15.269 3.94977 16.3349 3.65004 17.3372 3.90901C18.342 4.1692 19.1273 4.94854 19.3959 5.9497L20.288 9.28067C21.9965 9.26389 23.5674 10.3993 24.0299 12.1247C24.4916 13.8501 23.699 15.6186 22.2122 16.459ZM21.2506 20.2851L17.5433 6.44737C17.4534 6.11286 17.1908 5.85386 16.8575 5.76632C16.5218 5.67999 16.1681 5.78071 15.9271 6.0289L10.8757 11.2519C10.5136 11.6272 10.0556 11.897 9.55199 12.0324L8.38176 12.3454L10.3649 19.748L10.6107 19.682C10.6551 19.67 10.7018 19.6616 10.7462 19.6556L11.5375 19.4434C12.0423 19.3091 12.5735 19.3139 13.0746 19.4566L20.0613 21.4553C20.3934 21.5501 20.7508 21.4601 20.9978 21.2179C21.2436 20.9745 21.3405 20.6184 21.2506 20.2851ZM6.52923 12.8417L5.43214 13.1354C4.4094 13.41 3.8015 14.4615 4.07606 15.4854L5.06884 19.1903C5.32543 20.1507 6.26664 20.7442 7.22584 20.5883L8.51237 20.2442L6.52923 12.8417ZM21.6797 14.4747C22.1485 14.0059 22.3596 13.3057 22.1761 12.6222C21.9927 11.9364 21.4591 11.4364 20.82 11.2649L21.6797 14.4747ZM8.45469 22.2564L10.3155 26.8354C10.4978 27.285 10.9858 27.526 11.4522 27.4013L12.3778 27.1531C12.698 27.0668 12.9521 26.821 13.0481 26.5021C13.1428 26.1843 13.0672 25.839 12.8466 25.5896L12.1992 24.8606C12.0601 24.7035 11.939 24.5321 11.8371 24.3486L10.3875 21.7384L8.45469 22.2564ZM23.7937 3.8326C24.0586 3.37338 24.6449 3.21631 25.103 3.4813C25.5634 3.74628 25.7204 4.33258 25.4543 4.7918L24.4951 6.45244C24.2313 6.91166 23.6438 7.06872 23.1858 6.80374C22.7277 6.53876 22.5695 5.95245 22.8345 5.49323L23.7937 3.8326ZM27.4877 10.2054C27.9985 10.0687 28.5249 10.3721 28.6616 10.884C28.7995 11.396 28.4949 11.9212 27.9841 12.059L26.1305 12.5554C25.6197 12.6921 25.0933 12.3888 24.9555 11.8768C24.8188 11.3648 25.1221 10.8397 25.6341 10.7018L27.4877 10.2054ZM28.9301 17.7639C29.3894 18.0277 29.5464 18.614 29.2814 19.0744C29.0165 19.5324 28.4302 19.6895 27.9709 19.4245L26.3103 18.4653C25.8523 18.2003 25.694 17.614 25.959 17.1548C26.224 16.6968 26.8115 16.5397 27.2695 16.8035L28.9301 17.7639Z" fill="#5F9EA0"/>
</svg>
`
export const Bannersicon = `
<svg width="42" height="32" viewBox="0 0 42 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1" y="9" width="40" height="15" rx="3.89715" stroke="#5F9EA0" stroke-width="2"/>
<rect x="4.89711" y="13.2524" width="6.49524" height="6.49524" rx="1.29905" fill="#5F9EA0"/>
<path d="M34.1771 15.1753H15.9905C14.5702 15.1753 13.3924 15.1753 13.3924 14.201C13.3924 13.2267 14.5702 13.2267 15.9905 13.2267H34.1771C35.5974 13.2267 36.7752 13.2267 36.7752 14.201C36.7752 15.1753 35.6321 15.1753 34.1771 15.1753Z" fill="#5F9EA0"/>
<path d="M24.9479 19.7734H14.8368C14.0472 19.7734 13.3924 19.7734 13.3924 18.7734C13.3924 17.7734 14.0472 17.7734 14.8368 17.7734H24.9479C25.7375 17.7734 26.3924 17.7734 26.3924 18.7734C26.3924 19.7734 25.7568 19.7734 24.9479 19.7734Z" fill="#5F9EA0"/>
</svg>
`
export const Checklisticon = `
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M23.4922 12.8265H16.4922C15.9455 12.8265 15.4922 12.3732 15.4922 11.8265C15.4922 11.2799 15.9455 10.8265 16.4922 10.8265H23.4922C24.0389 10.8265 24.4922 11.2799 24.4922 11.8265C24.4922 12.3732 24.0522 12.8265 23.4922 12.8265Z" fill="#5F9EA0"/>
<path d="M9.49432 13.8396C9.24099 13.8396 8.98766 13.7463 8.78766 13.5463L7.78766 12.5463C7.40099 12.1596 7.40099 11.5196 7.78766 11.133C8.17432 10.7463 8.81432 10.7463 9.20099 11.133L9.49432 11.4263L11.7877 9.13296C12.1743 8.74629 12.8143 8.74629 13.201 9.13296C13.5877 9.51962 13.5877 10.1596 13.201 10.5463L10.201 13.5463C10.0143 13.733 9.76099 13.8396 9.49432 13.8396Z" fill="#5F9EA0"/>
<path d="M23.4922 22.1602H16.4922C15.9455 22.1602 15.4922 21.7068 15.4922 21.1602C15.4922 20.6135 15.9455 20.1602 16.4922 20.1602H23.4922C24.0389 20.1602 24.4922 20.6135 24.4922 21.1602C24.4922 21.7068 24.0522 22.1602 23.4922 22.1602Z" fill="#5F9EA0"/>
<path d="M9.49432 23.1732C9.24099 23.1732 8.98766 23.0799 8.78766 22.8799L7.78766 21.8799C7.40099 21.4932 7.40099 20.8532 7.78766 20.4666C8.17432 20.0799 8.81432 20.0799 9.20099 20.4666L9.49432 20.7599L11.7877 18.4666C12.1743 18.0799 12.8143 18.0799 13.201 18.4666C13.5877 18.8532 13.5877 19.4932 13.201 19.8799L10.201 22.8799C10.0143 23.0666 9.76099 23.1732 9.49432 23.1732Z" fill="#5F9EA0"/>
<path d="M19.9997 30.333H11.9997C4.75974 30.333 1.66641 27.2397 1.66641 19.9997V11.9997C1.66641 4.75972 4.75974 1.66638 11.9997 1.66638H19.9997C27.2397 1.66638 30.3331 4.75972 30.3331 11.9997V19.9997C30.3331 27.2397 27.2397 30.333 19.9997 30.333ZM11.9997 3.66638C5.85307 3.66638 3.66641 5.85305 3.66641 11.9997V19.9997C3.66641 26.1464 5.85307 28.333 11.9997 28.333H19.9997C26.1464 28.333 28.3331 26.1464 28.3331 19.9997V11.9997C28.3331 5.85305 26.1464 3.66638 19.9997 3.66638H11.9997Z" fill="#5F9EA0"/>
</svg>

`
export const Hotspoticon = `
<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.8336 25.1377L10.8337 25.1377L11.5218 24.5383L11.6692 24.566C11.6692 24.5659 11.6692 24.5659 11.6692 24.5658C11.7422 24.1771 11.4862 23.8031 11.0975 23.73L11.0975 23.73C5.98955 22.7713 2.28173 18.3004 2.28173 13.099C2.28173 7.13461 7.13459 2.28202 13.0993 2.28202C19.0639 2.28202 23.9171 7.13461 23.9171 13.099C23.9171 18.3004 20.2093 22.7713 15.1013 23.73L15.1013 23.73C14.7124 23.8031 14.4569 24.1772 14.5296 24.5658L14.5296 24.5659C14.5941 24.9102 14.8949 25.1499 15.2325 25.1499C15.2757 25.1499 15.3204 25.146 15.3652 25.1374C21.1506 24.0518 25.3491 18.9888 25.3491 13.099C25.3491 6.3446 19.8542 0.85 13.0995 0.85C6.3449 0.85 0.85 6.3446 0.85 13.099C0.85 18.9888 5.04846 24.0518 10.8336 25.1377Z" fill="#5F9EA0" stroke="#5F9EA0" stroke-width="0.3"/>
<path d="M12.1534 20.6C12.212 20.2882 12.0066 19.9882 11.6949 19.9296L11.6949 19.9296C8.36533 19.3046 5.94828 16.3902 5.94828 12.9996C5.94828 9.11178 9.11176 5.94847 12.9998 5.94847C16.8879 5.94847 20.0515 9.11178 20.0515 12.9996C20.0515 16.3902 17.6345 19.3046 14.3049 19.9296L14.3049 19.9296C13.993 19.9882 13.7882 20.2883 13.8464 20.5998L13.8464 20.5999C13.8982 20.8762 14.1395 21.0683 14.4103 21.0683C14.4447 21.0683 14.4805 21.0652 14.5166 21.0583C18.3896 20.3315 21.2 16.9423 21.2 12.9996C21.2 8.47806 17.5217 4.8 13 4.8C8.47827 4.8 4.8 8.47806 4.8 12.9996C4.8 16.9423 7.61044 20.3315 11.4832 21.0585L11.4833 21.0585C11.7948 21.1167 12.095 20.912 12.1534 20.6ZM12.1534 20.6L11.9568 20.5631M12.1534 20.6C12.1534 20.5999 12.1534 20.5999 12.1534 20.5998L11.9568 20.5631M11.9568 20.5631C11.9188 20.7663 11.7233 20.8999 11.5201 20.8619C7.74201 20.1527 5 16.8462 5 12.9996C5 8.58853 8.58872 5 13 5C17.4113 5 21 8.58853 21 12.9996C21 16.8462 18.258 20.1527 14.4797 20.8617C14.4565 20.8662 14.4331 20.8683 14.4103 20.8683C14.2339 20.8683 14.0767 20.7429 14.043 20.5631C14.005 20.3599 14.1386 20.1643 14.3418 20.1262C17.7661 19.4834 20.2515 16.4864 20.2515 12.9996C20.2515 9.00131 16.9983 5.74847 12.9998 5.74847C9.00131 5.74847 5.74828 9.00131 5.74828 12.9996C5.74828 16.4864 8.23376 19.4834 11.658 20.1262C11.8612 20.1643 11.995 20.3599 11.9568 20.5631Z" fill="#5F9EA0" stroke="#5F9EA0" stroke-width="0.4"/>
<circle cx="13" cy="13" r="4" fill="#5F9EA0"/>
</svg>`
export const Surveyicon = `
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M28 11.3333V22.6667C28 26.6667 26 29.3333 21.3333 29.3333H10.6667C6 29.3333 4 26.6667 4 22.6667V11.3333C4 7.33332 6 4.66666 10.6667 4.66666H21.3333C26 4.66666 28 7.33332 28 11.3333Z" stroke="#5F9EA0" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10 15H10.0001" stroke="#5F9EA0" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M14 15H22" stroke="#5F9EA0" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10 20H10.0001" stroke="#5F9EA0" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M14 20H22" stroke="#5F9EA0" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17.1111 3C17.1111 3.73333 16.6111 4.33333 16 4.33333C15.3889 4.33333 14.8889 3.73333 14.8889 3H11V5.66667C11 6.4 11.5 7 12.1111 7H19.8889C20.5 7 21 6.4 21 5.66667V3H17.1111Z" fill="#5F9EA0"/>
<path d="M16 0C14.35 0 13 1.35 13 3C13 4.65 14.35 6 16 6C17.65 6 19 4.65 19 3C19 1.35 17.65 0 16 0ZM16 4.5C15.175 4.5 14.5 3.825 14.5 3C14.5 2.175 15.175 1.5 16 1.5C16.825 1.5 17.5 2.175 17.5 3C17.5 3.825 16.825 4.5 16 4.5Z" fill="#5F9EA0"/>
</svg>

`
export const ListEditIcon = `
<svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2 16.5H3.425L13.2 6.725L11.775 5.3L2 15.075V16.5ZM1 18.5C0.716667 18.5 0.479167 18.4042 0.2875 18.2125C0.0958333 18.0208 0 17.7833 0 17.5V15.075C0 14.8083 0.05 14.5542 0.15 14.3125C0.25 14.0708 0.391667 13.8583 0.575 13.675L13.2 1.075C13.4 0.891667 13.6208 0.75 13.8625 0.65C14.1042 0.55 14.3583 0.5 14.625 0.5C14.8917 0.5 15.15 0.55 15.4 0.65C15.65 0.75 15.8667 0.9 16.05 1.1L17.425 2.5C17.625 2.68333 17.7708 2.9 17.8625 3.15C17.9542 3.4 18 3.65 18 3.9C18 4.16667 17.9542 4.42083 17.8625 4.6625C17.7708 4.90417 17.625 5.125 17.425 5.325L4.825 17.925C4.64167 18.1083 4.42917 18.25 4.1875 18.35C3.94583 18.45 3.69167 18.5 3.425 18.5H1ZM12.475 6.025L11.775 5.3L13.2 6.725L12.475 6.025Z" fill="#1C1B1F"/>
</svg>
`
export const CopyListIcon = `
<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_5124_4692" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25">
<rect y="0.5" width="24" height="24" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_5124_4692)">
<path d="M8 22.5C7.45 22.5 6.97917 22.3042 6.5875 21.9125C6.19583 21.5208 6 21.05 6 20.5V18.5H4C3.45 18.5 2.97917 18.3042 2.5875 17.9125C2.19583 17.5208 2 17.05 2 16.5V15.5C2 15.2167 2.09583 14.9792 2.2875 14.7875C2.47917 14.5958 2.71667 14.5 3 14.5C3.28333 14.5 3.52083 14.5958 3.7125 14.7875C3.90417 14.9792 4 15.2167 4 15.5V16.5H6V8.5C6 7.95 6.19583 7.47917 6.5875 7.0875C6.97917 6.69583 7.45 6.5 8 6.5H16V4.5H15C14.7167 4.5 14.4792 4.40417 14.2875 4.2125C14.0958 4.02083 14 3.78333 14 3.5C14 3.21667 14.0958 2.97917 14.2875 2.7875C14.4792 2.59583 14.7167 2.5 15 2.5H16C16.55 2.5 17.0208 2.69583 17.4125 3.0875C17.8042 3.47917 18 3.95 18 4.5V6.5H20C20.55 6.5 21.0208 6.69583 21.4125 7.0875C21.8042 7.47917 22 7.95 22 8.5V20.5C22 21.05 21.8042 21.5208 21.4125 21.9125C21.0208 22.3042 20.55 22.5 20 22.5H8ZM8 20.5H20V8.5H8V20.5ZM3 12.5C2.71667 12.5 2.47917 12.4042 2.2875 12.2125C2.09583 12.0208 2 11.7833 2 11.5V9.5C2 9.21667 2.09583 8.97917 2.2875 8.7875C2.47917 8.59583 2.71667 8.5 3 8.5C3.28333 8.5 3.52083 8.59583 3.7125 8.7875C3.90417 8.97917 4 9.21667 4 9.5V11.5C4 11.7833 3.90417 12.0208 3.7125 12.2125C3.52083 12.4042 3.28333 12.5 3 12.5ZM3 6.5C2.71667 6.5 2.47917 6.40417 2.2875 6.2125C2.09583 6.02083 2 5.78333 2 5.5V4.5C2 3.95 2.19583 3.47917 2.5875 3.0875C2.97917 2.69583 3.45 2.5 4 2.5H5C5.28333 2.5 5.52083 2.59583 5.7125 2.7875C5.90417 2.97917 6 3.21667 6 3.5C6 3.78333 5.90417 4.02083 5.7125 4.2125C5.52083 4.40417 5.28333 4.5 5 4.5H4V5.5C4 5.78333 3.90417 6.02083 3.7125 6.2125C3.52083 6.40417 3.28333 6.5 3 6.5ZM9 4.5C8.71667 4.5 8.47917 4.40417 8.2875 4.2125C8.09583 4.02083 8 3.78333 8 3.5C8 3.21667 8.09583 2.97917 8.2875 2.7875C8.47917 2.59583 8.71667 2.5 9 2.5H11C11.2833 2.5 11.5208 2.59583 11.7125 2.7875C11.9042 2.97917 12 3.21667 12 3.5C12 3.78333 11.9042 4.02083 11.7125 4.2125C11.5208 4.40417 11.2833 4.5 11 4.5H9Z" fill="#1C1B1F"/>
</g>
</svg>
`
export const DeleteIconList = `
<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_5124_4700" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25">
<rect y="0.5" width="24" height="24" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_5124_4700)">
<path d="M7 21.5C6.45 21.5 5.97917 21.3042 5.5875 20.9125C5.19583 20.5208 5 20.05 5 19.5V6.5C4.71667 6.5 4.47917 6.40417 4.2875 6.2125C4.09583 6.02083 4 5.78333 4 5.5C4 5.21667 4.09583 4.97917 4.2875 4.7875C4.47917 4.59583 4.71667 4.5 5 4.5H9C9 4.21667 9.09583 3.97917 9.2875 3.7875C9.47917 3.59583 9.71667 3.5 10 3.5H14C14.2833 3.5 14.5208 3.59583 14.7125 3.7875C14.9042 3.97917 15 4.21667 15 4.5H19C19.2833 4.5 19.5208 4.59583 19.7125 4.7875C19.9042 4.97917 20 5.21667 20 5.5C20 5.78333 19.9042 6.02083 19.7125 6.2125C19.5208 6.40417 19.2833 6.5 19 6.5V19.5C19 20.05 18.8042 20.5208 18.4125 20.9125C18.0208 21.3042 17.55 21.5 17 21.5H7ZM17 6.5H7V19.5H17V6.5ZM10 17.5C10.2833 17.5 10.5208 17.4042 10.7125 17.2125C10.9042 17.0208 11 16.7833 11 16.5V9.5C11 9.21667 10.9042 8.97917 10.7125 8.7875C10.5208 8.59583 10.2833 8.5 10 8.5C9.71667 8.5 9.47917 8.59583 9.2875 8.7875C9.09583 8.97917 9 9.21667 9 9.5V16.5C9 16.7833 9.09583 17.0208 9.2875 17.2125C9.47917 17.4042 9.71667 17.5 10 17.5ZM14 17.5C14.2833 17.5 14.5208 17.4042 14.7125 17.2125C14.9042 17.0208 15 16.7833 15 16.5V9.5C15 9.21667 14.9042 8.97917 14.7125 8.7875C14.5208 8.59583 14.2833 8.5 14 8.5C13.7167 8.5 13.4792 8.59583 13.2875 8.7875C13.0958 8.97917 13 9.21667 13 9.5V16.5C13 16.7833 13.0958 17.0208 13.2875 17.2125C13.4792 17.4042 13.7167 17.5 14 17.5Z" fill="#1C1B1F"/>
</g>
</svg>
`
export const ArrowListButton = `
<svg width="10" height="18" viewBox="0 0 10 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.779297 1L8.06206 9L0.779297 17" stroke="#222222" stroke-width="2"/>
</svg>
`
export const editicon = `
<svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2 16.5H3.425L13.2 6.725L11.775 5.3L2 15.075V16.5ZM0 18.5V14.25L13.2 1.075C13.4 0.891667 13.6208 0.75 13.8625 0.65C14.1042 0.55 14.3583 0.5 14.625 0.5C14.8917 0.5 15.15 0.55 15.4 0.65C15.65 0.75 15.8667 0.9 16.05 1.1L17.425 2.5C17.625 2.68333 17.7708 2.9 17.8625 3.15C17.9542 3.4 18 3.65 18 3.9C18 4.16667 17.9542 4.42083 17.8625 4.6625C17.7708 4.90417 17.625 5.125 17.425 5.325L4.25 18.5H0ZM12.475 6.025L11.775 5.3L13.2 6.725L12.475 6.025Z" fill="#949494"/>
</svg>
 `

export const CrossIcon = `
<svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1.4 9.9999L0 8.5999L3.6 4.9999L0 1.4249L1.4 0.0249023L5 3.6249L8.575 0.0249023L9.975 1.4249L6.375 4.9999L9.975 8.5999L8.575 9.9999L5 6.3999L1.4 9.9999Z" fill="#AFAFAF"/>
</svg> 
`;

export const Settings = `
<svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.74922 20L7.34922 16.8C7.13255 16.7167 6.92839 16.6167 6.73672 16.5C6.54505 16.3833 6.35755 16.2583 6.17422 16.125L3.19922 17.375L0.449219 12.625L3.02422 10.675C3.00755 10.5583 2.99922 10.4458 2.99922 10.3375V9.6625C2.99922 9.55417 3.00755 9.44167 3.02422 9.325L0.449219 7.375L3.19922 2.625L6.17422 3.875C6.35755 3.74167 6.54922 3.61667 6.74922 3.5C6.94922 3.38333 7.14922 3.28333 7.34922 3.2L7.74922 0H13.2492L13.6492 3.2C13.8659 3.28333 14.0701 3.38333 14.2617 3.5C14.4534 3.61667 14.6409 3.74167 14.8242 3.875L17.7992 2.625L20.5492 7.375L17.9742 9.325C17.9909 9.44167 17.9992 9.55417 17.9992 9.6625V10.3375C17.9992 10.4458 17.9826 10.5583 17.9492 10.675L20.5242 12.625L17.7742 17.375L14.8242 16.125C14.6409 16.2583 14.4492 16.3833 14.2492 16.5C14.0492 16.6167 13.8492 16.7167 13.6492 16.8L13.2492 20H7.74922ZM9.49922 18H11.4742L11.8242 15.35C12.3409 15.2167 12.8201 15.0208 13.2617 14.7625C13.7034 14.5042 14.1076 14.1917 14.4742 13.825L16.9492 14.85L17.9242 13.15L15.7742 11.525C15.8576 11.2917 15.9159 11.0458 15.9492 10.7875C15.9826 10.5292 15.9992 10.2667 15.9992 10C15.9992 9.73333 15.9826 9.47083 15.9492 9.2125C15.9159 8.95417 15.8576 8.70833 15.7742 8.475L17.9242 6.85L16.9492 5.15L14.4742 6.2C14.1076 5.81667 13.7034 5.49583 13.2617 5.2375C12.8201 4.97917 12.3409 4.78333 11.8242 4.65L11.4992 2H9.52422L9.17422 4.65C8.65755 4.78333 8.17839 4.97917 7.73672 5.2375C7.29505 5.49583 6.89089 5.80833 6.52422 6.175L4.04922 5.15L3.07422 6.85L5.22422 8.45C5.14089 8.7 5.08255 8.95 5.04922 9.2C5.01589 9.45 4.99922 9.71667 4.99922 10C4.99922 10.2667 5.01589 10.525 5.04922 10.775C5.08255 11.025 5.14089 11.275 5.22422 11.525L3.07422 13.15L4.04922 14.85L6.52422 13.8C6.89089 14.1833 7.29505 14.5042 7.73672 14.7625C8.17839 15.0208 8.65755 15.2167 9.17422 15.35L9.49922 18ZM10.5492 13.5C11.5159 13.5 12.3409 13.1583 13.0242 12.475C13.7076 11.7917 14.0492 10.9667 14.0492 10C14.0492 9.03333 13.7076 8.20833 13.0242 7.525C12.3409 6.84167 11.5159 6.5 10.5492 6.5C9.56589 6.5 8.73672 6.84167 8.06172 7.525C7.38672 8.20833 7.04922 9.03333 7.04922 10C7.04922 10.9667 7.38672 11.7917 8.06172 12.475C8.73672 13.1583 9.56589 13.5 10.5492 13.5Z" fill="#1C1B1F"/>
</svg>
`;

export const ClearFormat = `<svg width="38" height="32" viewBox="0 0 38 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M23.3248 15.9874L21.9248 14.5874L23.9748 12.5374L22.5498 11.1124L20.4998 13.1624L19.0998 11.7624L23.9748 6.8874C24.1748 6.6874 24.3956 6.54157 24.6373 6.4499C24.879 6.35824 25.1331 6.3124 25.3998 6.3124C25.6665 6.3124 25.9248 6.3624 26.1748 6.4624C26.4248 6.5624 26.6415 6.7124 26.8248 6.9124L28.1998 8.3124C28.3998 8.49574 28.5456 8.7124 28.6373 8.9624C28.729 9.2124 28.7748 9.4624 28.7748 9.7124C28.7748 9.97907 28.729 10.2332 28.6373 10.4749C28.5456 10.7166 28.3998 10.9374 28.1998 11.1374L23.3248 15.9874ZM12.7748 22.3124H14.1998L19.0748 17.4374L18.3748 16.7124L17.6498 16.0124L12.7748 20.8874V22.3124ZM27.5748 25.9124L20.4998 18.8624L15.0248 24.3124H10.7748V20.0874L16.2498 14.6124L9.1748 7.5124L10.5998 6.0874L28.9998 24.4874L27.5748 25.9124ZM18.3748 16.7124L17.6498 16.0124L19.0748 17.4374L18.3748 16.7124Z" fill="#1C1B1F"/>
</svg>`;

export const PublishAndMore = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_7028_1758" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
<rect width="24" height="24" fill="#5F9EA0"/>
</mask>
<g mask="url(#mask0_7028_1758)">
<path d="M9.2502 22L8.8502 18.8C8.63353 18.7167 8.42936 18.6167 8.2377 18.5C8.04603 18.3833 7.85853 18.2583 7.6752 18.125L4.7002 19.375L1.9502 14.625L4.5252 12.675C4.50853 12.5583 4.5002 12.4458 4.5002 12.3375V11.6625C4.5002 11.5542 4.50853 11.4417 4.5252 11.325L1.9502 9.375L4.7002 4.625L7.6752 5.875C7.85853 5.74167 8.0502 5.61667 8.2502 5.5C8.4502 5.38333 8.6502 5.28333 8.8502 5.2L9.2502 2H14.7502L15.1502 5.2C15.3669 5.28333 15.571 5.38333 15.7627 5.5C15.9544 5.61667 16.1419 5.74167 16.3252 5.875L19.3002 4.625L22.0502 9.375L20.2002 10.775C19.8335 10.5917 19.4585 10.4375 19.0752 10.3125C18.6919 10.1875 18.2835 10.1 17.8502 10.05L19.4252 8.85L18.4502 7.15L15.9752 8.2C15.6085 7.81667 15.2044 7.49583 14.7627 7.2375C14.321 6.97917 13.8419 6.78333 13.3252 6.65L13.0002 4H11.0252L10.6752 6.65C10.1585 6.78333 9.67936 6.97917 9.2377 7.2375C8.79603 7.49583 8.39186 7.80833 8.0252 8.175L5.5502 7.15L4.5752 8.85L6.7252 10.45C6.64186 10.7 6.58353 10.95 6.5502 11.2C6.51686 11.45 6.5002 11.7167 6.5002 12C6.5002 12.2667 6.51686 12.525 6.5502 12.775C6.58353 13.025 6.64186 13.275 6.7252 13.525L4.5752 15.15L5.5502 16.85L8.0252 15.8C8.30853 16.0833 8.6127 16.3375 8.9377 16.5625C9.2627 16.7875 9.61686 16.975 10.0002 17.125C10.0169 18.075 10.2127 18.9667 10.5877 19.8C10.9627 20.6333 11.4669 21.3667 12.1002 22H9.2502ZM10.2752 15.025C10.3752 14.6917 10.496 14.3708 10.6377 14.0625C10.7794 13.7542 10.9419 13.4583 11.1252 13.175C10.9419 13.0417 10.8002 12.8708 10.7002 12.6625C10.6002 12.4542 10.5502 12.2333 10.5502 12C10.5502 11.5833 10.696 11.2292 10.9877 10.9375C11.2794 10.6458 11.6335 10.5 12.0502 10.5C12.2835 10.5 12.5085 10.5542 12.7252 10.6625C12.9419 10.7708 13.1169 10.9167 13.2502 11.1C13.5335 10.9167 13.8252 10.7542 14.1252 10.6125C14.4252 10.4708 14.7419 10.3583 15.0752 10.275C14.7752 9.74167 14.3585 9.3125 13.8252 8.9875C13.2919 8.6625 12.7002 8.5 12.0502 8.5C11.0669 8.5 10.2377 8.84167 9.5627 9.525C8.8877 10.2083 8.5502 11.0333 8.5502 12C8.5502 12.6333 8.70436 13.2208 9.0127 13.7625C9.32103 14.3042 9.74186 14.725 10.2752 15.025ZM17.0002 20L20.0002 17L17.0002 14L16.3002 14.7L18.1002 16.5H14.0002V17.5H18.0752L16.3002 19.3L17.0002 20ZM17.0002 22C15.6169 22 14.4377 21.5125 13.4627 20.5375C12.4877 19.5625 12.0002 18.3833 12.0002 17C12.0002 15.6167 12.4877 14.4375 13.4627 13.4625C14.4377 12.4875 15.6169 12 17.0002 12C18.3835 12 19.5627 12.4875 20.5377 13.4625C21.5127 14.4375 22.0002 15.6167 22.0002 17C22.0002 18.3833 21.5127 19.5625 20.5377 20.5375C19.5627 21.5125 18.3835 22 17.0002 22Z" fill="#5F9EA0"/>
</g>
</svg>`;

export const Save = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_7028_1769" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
<rect width="24" height="24" fill="#5F9EA0"/>
</mask>
<g mask="url(#mask0_7028_1769)">
<path d="M21 7V19C21 19.55 20.8042 20.0208 20.4125 20.4125C20.0208 20.8042 19.55 21 19 21H5C4.45 21 3.97917 20.8042 3.5875 20.4125C3.19583 20.0208 3 19.55 3 19V5C3 4.45 3.19583 3.97917 3.5875 3.5875C3.97917 3.19583 4.45 3 5 3H17L21 7ZM19 7.85L16.15 5H5V19H19V7.85ZM12 18C12.8333 18 13.5417 17.7083 14.125 17.125C14.7083 16.5417 15 15.8333 15 15C15 14.1667 14.7083 13.4583 14.125 12.875C13.5417 12.2917 12.8333 12 12 12C11.1667 12 10.4583 12.2917 9.875 12.875C9.29167 13.4583 9 14.1667 9 15C9 15.8333 9.29167 16.5417 9.875 17.125C10.4583 17.7083 11.1667 18 12 18ZM6 10H15V6H6V10Z" fill="#5F9EA0"/>
</g>
</svg>`;


export const errorIcon =`<svg width="45" height="44" viewBox="0 0 45 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="0.5" width="44" height="44" rx="22" fill="#D93030" fill-opacity="0.2"/>
<path d="M22.5 27C22.7833 27 23.0208 26.9042 23.2125 26.7125C23.4042 26.5208 23.5 26.2833 23.5 26C23.5 25.7167 23.4042 25.4792 23.2125 25.2875C23.0208 25.0958 22.7833 25 22.5 25C22.2167 25 21.9792 25.0958 21.7875 25.2875C21.5958 25.4792 21.5 25.7167 21.5 26C21.5 26.2833 21.5958 26.5208 21.7875 26.7125C21.9792 26.9042 22.2167 27 22.5 27ZM21.5 23H23.5V17H21.5V23ZM22.5 32C21.1167 32 19.8167 31.7375 18.6 31.2125C17.3833 30.6875 16.325 29.975 15.425 29.075C14.525 28.175 13.8125 27.1167 13.2875 25.9C12.7625 24.6833 12.5 23.3833 12.5 22C12.5 20.6167 12.7625 19.3167 13.2875 18.1C13.8125 16.8833 14.525 15.825 15.425 14.925C16.325 14.025 17.3833 13.3125 18.6 12.7875C19.8167 12.2625 21.1167 12 22.5 12C23.8833 12 25.1833 12.2625 26.4 12.7875C27.6167 13.3125 28.675 14.025 29.575 14.925C30.475 15.825 31.1875 16.8833 31.7125 18.1C32.2375 19.3167 32.5 20.6167 32.5 22C32.5 23.3833 32.2375 24.6833 31.7125 25.9C31.1875 27.1167 30.475 28.175 29.575 29.075C28.675 29.975 27.6167 30.6875 26.4 31.2125C25.1833 31.7375 23.8833 32 22.5 32ZM22.5 30C24.7333 30 26.625 29.225 28.175 27.675C29.725 26.125 30.5 24.2333 30.5 22C30.5 19.7667 29.725 17.875 28.175 16.325C26.625 14.775 24.7333 14 22.5 14C20.2667 14 18.375 14.775 16.825 16.325C15.275 17.875 14.5 19.7667 14.5 22C14.5 24.2333 15.275 26.125 16.825 27.675C18.375 29.225 20.2667 30 22.5 30Z" fill="#D93030"/>
</svg>`

export const QuestionFill =`<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 1C5.925 1 1 5.925 1 12C1 18.075 5.925 23 12 23C18.075 23 23 18.075 23 12C23 5.925 18.075 1 12 1ZM10.892 8.935C11.122 8.482 11.292 8.267 11.433 8.155C11.539 8.071 11.683 8 12 8C12.625 8 13 8.47 13 8.978C13 9.256 12.946 9.394 12.798 9.57C12.591 9.816 12.208 10.115 11.45 10.616L11 10.912V13C11 13.2652 11.1054 13.5196 11.2929 13.7071C11.4804 13.8946 11.7348 14 12 14C12.2652 14 12.5196 13.8946 12.7071 13.7071C12.8946 13.5196 13 13.2652 13 13V11.983C13.542 11.609 13.997 11.251 14.327 10.859C14.804 10.293 15 9.689 15 8.978C15 7.508 13.867 6 12 6C11.316 6 10.711 6.176 10.192 6.587C9.708 6.97 9.378 7.497 9.108 8.032C9.04526 8.14947 9.00661 8.27828 8.99433 8.41089C8.98205 8.54349 8.99637 8.67722 9.03647 8.80421C9.07657 8.9312 9.14163 9.04891 9.22783 9.15042C9.31403 9.25193 9.41964 9.3352 9.53846 9.39534C9.65728 9.45548 9.78691 9.49128 9.91975 9.50065C10.0526 9.51001 10.186 9.49275 10.3121 9.44987C10.4381 9.40699 10.5544 9.33936 10.654 9.25094C10.7536 9.16253 10.8345 9.05511 10.892 8.935ZM13 16.5C13 16.2348 12.8946 15.9804 12.7071 15.7929C12.5196 15.6054 12.2652 15.5 12 15.5C11.7348 15.5 11.4804 15.6054 11.2929 15.7929C11.1054 15.9804 11 16.2348 11 16.5V17C11 17.2652 11.1054 17.5196 11.2929 17.7071C11.4804 17.8946 11.7348 18 12 18C12.2652 18 12.5196 17.8946 12.7071 17.7071C12.8946 17.5196 13 17.2652 13 17V16.5Z" fill="black"/>
</svg>`

export const InfoFilled = `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12 1.5C14.785 1.5 17.4559 2.60633 19.4252 4.5756C21.3944 6.54487 22.5008 9.21578 22.5008 12.0007C22.5008 14.7857 21.3944 17.4566 19.4252 19.4259C17.4559 21.3952 14.785 22.5015 12 22.5015C9.21505 22.5015 6.54414 21.3952 4.57487 19.4259C2.60559 17.4566 1.49927 14.7857 1.49927 12.0007C1.49927 9.21578 2.60559 6.54487 4.57487 4.5756C6.54414 2.60633 9.21505 1.5 12 1.5ZM13.575 7.947C14.355 7.947 14.988 7.4055 14.988 6.603C14.988 5.8005 14.3535 5.259 13.575 5.259C12.795 5.259 12.165 5.8005 12.165 6.603C12.165 7.4055 12.795 7.947 13.575 7.947ZM13.8495 16.3875C13.8495 16.227 13.905 15.81 13.8735 15.573L12.6405 16.992C12.3855 17.2605 12.066 17.4465 11.916 17.397C11.848 17.372 11.7911 17.3235 11.7556 17.2602C11.7201 17.197 11.7084 17.1231 11.7225 17.052L13.7775 10.56C13.9455 9.7365 13.4835 8.985 12.504 8.889C11.4705 8.889 9.94952 9.9375 9.02402 11.268C9.02402 11.427 8.99402 11.823 9.02552 12.06L10.257 10.6395C10.512 10.374 10.809 10.1865 10.959 10.2375C11.0329 10.264 11.0935 10.3185 11.1277 10.3891C11.1619 10.4598 11.167 10.5411 11.142 10.6155L9.10502 17.076C8.86952 17.832 9.31502 18.573 10.395 18.741C11.985 18.741 12.924 17.718 13.851 16.3875H13.8495Z" fill="black"/>
</svg>`

export const Solid =`<svg width="20" height="20" viewBox="0 0 21 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 11C0 13.7848 1.10625 16.4555 3.07538 18.4246C5.04451 20.3938 7.71523 21.5 10.5 21.5C13.2848 21.5 15.9555 20.3938 17.9246 18.4246C19.8938 16.4555 21 13.7848 21 11C21 8.21523 19.8938 5.54451 17.9246 3.57538C15.9555 1.60625 13.2848 0.5 10.5 0.5C7.71523 0.5 5.04451 1.60625 3.07538 3.57538C1.10625 5.54451 0 8.21523 0 11Z" fill="#494747"/>
</svg>`

export const Reselect = `<svg width="15" height="26" viewBox="0 0 15 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5.65775 2.77941C5.98178 2.77941 6.22481 2.53638 6.22481 2.21235V0.727211C6.22481 0.40318 5.98178 0.160156 5.65775 0.160156C5.33372 0.160156 5.0907 0.40318 5.0907 0.727211V2.21235C5.0907 2.53638 5.36072 2.77941 5.65775 2.77941Z" fill="#797979"/>
<path d="M2.98416 3.56279C3.09217 3.6708 3.25419 3.72481 3.3892 3.72481C3.52421 3.72481 3.68623 3.6708 3.79424 3.56279C4.01026 3.34677 4.01026 2.96874 3.79424 2.75271L2.74114 1.69961C2.52512 1.48359 2.14708 1.48359 1.93106 1.69961C1.71504 1.91563 1.71504 2.29367 1.93106 2.50969L2.98416 3.56279Z" fill="#797979"/>
<path d="M7.92631 3.72481C8.06132 3.72481 8.22334 3.6708 8.33135 3.56279L9.38445 2.50969C9.60047 2.29367 9.60047 1.91563 9.38445 1.69961C9.16843 1.48359 8.79039 1.48359 8.57437 1.69961L7.52127 2.75271C7.30525 2.96874 7.30525 3.34677 7.52127 3.56279C7.62928 3.6708 7.79129 3.72481 7.92631 3.72481Z" fill="#797979"/>
<path d="M0.986391 12.7703H2.98458V11.6091H1.55345V5.9386H9.76224V11.6091H8.3581V12.7703H10.3563C10.6803 12.7703 10.9503 12.5002 10.9503 12.1762V5.34455C10.9503 5.02051 10.6803 4.75049 10.3563 4.75049H0.986391C0.66236 4.75049 0.392334 4.99351 0.392334 5.34455V12.1762C0.392334 12.5002 0.66236 12.7703 0.986391 12.7703Z" fill="#797979"/>
<path d="M14.9999 17.1718C14.9999 16.3888 14.3519 15.7407 13.5688 15.7407C13.0558 15.7407 12.6237 16.0107 12.3537 16.4158V16.3888C12.3537 15.6057 11.7056 14.9576 10.9226 14.9576C10.4095 14.9576 9.97746 15.2276 9.70743 15.6327V15.6057C9.70743 14.8226 9.05937 14.1745 8.2763 14.1745C7.76325 14.1745 7.33121 14.4446 7.06118 14.8496V10.0702C7.06118 9.69211 6.89916 9.34108 6.62914 9.07105C6.35911 8.80103 6.00808 8.66602 5.63004 8.66602C4.84697 8.66602 4.1989 9.31408 4.1989 10.0972V18.9C4.1989 18.981 4.1449 19.062 4.06389 19.089C3.98288 19.116 3.90188 19.116 3.84787 19.035L2.47074 17.5769C1.98469 17.0908 1.20162 17.0098 0.634561 17.4149C0.283528 17.6309 0.0675065 18.0089 0.0135013 18.414C-0.0405039 18.819 0.0675067 19.224 0.337533 19.5481L3.92888 23.8955C4.95498 25.1376 6.44012 25.8397 8.06028 25.8397H10.3015C11.5436 25.8397 12.7317 25.3536 13.6228 24.4625C14.5139 23.5714 14.9999 22.3833 14.9999 21.1412V17.1718Z" fill="#797979"/>
</svg>`

export const NoData = `<svg width="385" height="256" viewBox="0 0 385 256" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.8">
<path d="M112.874 62.5971L55.2773 101.025L112.01 185.615L169.606 147.186L112.874 62.5971Z" fill="#F5F5F5"/>
<path d="M139.844 143.186L139.192 147.22L110.559 143.104L111.211 139.069L139.844 143.186Z" fill="#E0E0E0"/>
<path d="M125.422 128.793L129.528 129.382L124.975 157.501L120.863 156.907L125.422 128.793Z" fill="#E0E0E0"/>
<path d="M99.0732 93.0554C99.1143 93.1117 93.3444 97.0388 86.1834 101.811C79.0224 106.582 73.1858 110.412 73.1447 110.351C73.1037 110.289 78.8735 106.367 86.0345 101.59C93.1955 96.8135 99.0065 92.994 99.0732 93.0554Z" fill="#E0E0E0"/>
<path d="M120.235 87.7514C120.276 87.8129 110.676 94.2845 98.7979 102.21C86.9194 110.136 77.2379 116.51 77.2071 116.454C77.1763 116.398 86.7551 109.916 98.649 101.99C110.543 94.0644 120.194 87.69 120.235 87.7514Z" fill="#E0E0E0"/>
<path d="M124.477 94.0795C124.518 94.141 114.919 100.618 103.04 108.544C91.1616 116.469 81.4801 122.844 81.4493 122.782C81.4185 122.721 91.0076 116.249 102.891 108.318C114.775 100.387 124.436 94.0181 124.477 94.0795Z" fill="#E0E0E0"/>
<path d="M128.723 100.414C128.764 100.475 119.165 106.952 107.287 114.872C95.408 122.793 85.7266 129.178 85.7266 129.116C85.7266 129.055 95.2797 122.578 107.163 114.652C119.047 106.726 128.682 100.352 128.723 100.414Z" fill="#E0E0E0"/>
<path d="M132.952 106.747C132.993 106.808 123.394 113.28 111.515 121.205C99.6369 129.131 89.9554 135.506 89.9297 135.444C89.9041 135.383 99.4983 128.911 111.392 120.985C123.286 113.059 132.952 106.685 132.952 106.747Z" fill="#E0E0E0"/>
<path d="M305.469 119.277L257.098 151.551L304.742 222.59L353.114 190.317L305.469 119.277Z" fill="#F5F5F5"/>
<path d="M328.122 186.972L327.573 190.361L303.523 186.905L304.078 183.516L328.122 186.972Z" fill="#E0E0E0"/>
<path d="M316.013 174.884L319.463 175.38L315.638 198.994L312.184 198.497L316.013 174.884Z" fill="#E0E0E0"/>
<path d="M293.863 144.895C293.904 144.957 289.063 148.259 283.047 152.268C277.031 156.277 272.123 159.482 272.082 159.436C272.041 159.39 276.887 156.077 282.898 152.063C288.909 148.049 293.822 144.808 293.863 144.895Z" fill="#E0E0E0"/>
<path d="M311.651 140.416C311.692 140.477 303.638 145.925 293.684 152.576C283.731 159.226 275.584 164.582 275.543 164.521C275.502 164.459 283.556 159.011 293.51 152.355C303.463 145.699 311.595 140.354 311.651 140.416Z" fill="#E0E0E0"/>
<path d="M315.217 145.73C315.258 145.791 307.204 151.239 297.251 157.895C287.297 164.551 279.151 169.896 279.11 169.835C279.068 169.773 287.097 164.351 297.076 157.695C307.055 151.039 315.187 145.674 315.217 145.73Z" fill="#E0E0E0"/>
<path d="M318.78 151.039C318.821 151.101 310.762 156.549 300.813 163.205C290.865 169.861 282.713 175.206 282.672 175.144C282.631 175.083 290.685 169.64 300.639 162.979C310.592 156.318 318.744 150.988 318.78 151.039Z" fill="#E0E0E0"/>
<path d="M322.374 156.37C322.415 156.431 314.361 161.874 304.407 168.53C294.453 175.186 286.307 180.531 286.266 180.475C286.225 180.418 294.279 174.966 304.232 168.31C314.186 161.654 322.307 156.308 322.374 156.37Z" fill="#E0E0E0"/>
<path d="M228.944 65.7095L176.164 37.5537L134.598 115.068L187.377 143.224L228.944 65.7095Z" fill="#F5F5F5"/>
<path d="M178.344 118.974L174.997 120.115L166.496 96.6811L169.848 95.5342L178.344 118.974Z" fill="#E0E0E0"/>
<path d="M183.477 102.159L184.698 105.523L161.362 113.495L160.141 110.131L183.477 102.159Z" fill="#E0E0E0"/>
<path d="M200.055 67.5837C200.014 67.6605 194.665 64.8855 188.1 61.3885C181.534 57.8916 176.252 54.9885 176.293 54.9117C176.334 54.8349 181.688 57.61 188.254 61.1069C194.819 64.6039 200.096 67.5018 200.055 67.5837Z" fill="#E0E0E0"/>
<path d="M212.725 81.5251C212.684 81.6019 203.824 76.953 192.936 71.1469C182.048 65.3408 173.26 60.5689 173.301 60.4921C173.342 60.4153 182.197 65.0592 193.085 70.8704C203.973 76.6816 212.766 81.4483 212.725 81.5251Z" fill="#E0E0E0"/>
<path d="M209.616 87.3269C209.574 87.4037 200.714 82.7598 189.827 76.9486C178.939 71.1374 170.15 66.3707 170.192 66.2939C170.233 66.2171 179.088 70.8661 189.975 76.6721C200.863 82.4782 209.657 87.2501 209.616 87.3269Z" fill="#E0E0E0"/>
<path d="M206.502 93.1278C206.461 93.2046 197.601 88.5608 186.713 82.7547C175.825 76.9486 167.037 72.1921 167.078 72.1C167.119 72.0078 175.974 76.667 186.862 82.4731C197.75 88.2792 206.543 93.051 206.502 93.1278Z" fill="#E0E0E0"/>
<path d="M203.393 98.9282C203.352 99.005 194.492 94.3612 183.604 88.5551C172.716 82.749 163.923 77.9772 163.969 77.9004C164.015 77.8236 172.865 82.4674 183.753 88.2735C194.641 94.0796 203.434 98.8514 203.393 98.9282Z" fill="#E0E0E0"/>
<path d="M340.684 225.193C340.684 225.264 274.336 225.326 192.505 225.326C110.675 225.326 44.3164 225.28 44.3164 225.193C44.3164 225.106 110.654 225.06 192.505 225.06C274.356 225.06 340.684 225.116 340.684 225.193Z" fill="#263238"/>
<path d="M60.719 193.126C61.5869 193.189 62.4333 193.425 63.2076 193.821C63.982 194.217 64.6684 194.764 65.2261 195.43C66.3348 196.771 67.1198 198.347 67.5207 200.038C68.3882 203.274 67.8543 207.099 66.9303 210.319C63.4191 209.065 61.2477 205.327 60.257 203.361C58.717 200.258 57.7981 193.705 60.7036 193.121" fill="#5F9EA0"/>
<path d="M71.9891 214.246C71.4619 213.43 71.1604 212.49 71.1153 211.521C71.0702 210.551 71.2831 209.587 71.7325 208.726C72.1959 207.874 72.8249 207.122 73.5829 206.514C74.3409 205.907 75.2126 205.456 76.1471 205.189C77.0044 204.922 78.0003 204.815 78.7446 205.311C79.0801 205.565 79.3414 205.903 79.5013 206.291C79.6611 206.679 79.7135 207.103 79.6532 207.518C79.5185 208.35 79.1641 209.131 78.6265 209.781C76.9171 212.09 74.8433 214.01 71.9532 214.246" fill="#5F9EA0"/>
<path d="M72.3798 225.064C72.3036 224.847 72.2503 224.622 72.2206 224.394C72.1282 223.912 72.005 223.293 71.8562 222.551C71.3954 220.518 71.2671 218.424 71.4763 216.35C71.7184 214.266 72.5141 212.285 73.7812 210.611C74.1683 210.11 74.6147 209.658 75.1107 209.264C75.2455 209.158 75.386 209.058 75.5316 208.967C75.578 208.929 75.6299 208.898 75.6856 208.875C75.0363 209.428 74.4431 210.044 73.9146 210.713C72.7143 212.389 71.9586 214.341 71.7176 216.386C71.389 218.772 71.7946 220.958 72.0307 222.53C72.1539 223.313 72.2566 223.948 72.313 224.389C72.3535 224.612 72.3758 224.838 72.3798 225.064Z" fill="#263238"/>
<path d="M61.8516 198.057C61.9102 198.137 61.9585 198.225 61.9953 198.318C62.0877 198.523 62.2109 198.779 62.3546 199.101C62.6678 199.782 63.1041 200.77 63.6328 201.994C64.6903 204.446 66.1071 207.856 67.5855 211.655C69.0639 215.454 70.3216 218.921 71.1788 221.445C71.6203 222.704 71.9642 223.728 72.2055 224.44C72.3133 224.778 72.3954 225.049 72.4622 225.259C72.4988 225.353 72.5246 225.451 72.5392 225.551C72.4871 225.464 72.4457 225.371 72.416 225.275C72.339 225.07 72.2414 224.804 72.1131 224.471L71.03 221.501C70.1111 218.997 68.8278 215.536 67.3545 211.742C65.8812 207.948 64.485 204.528 63.4686 202.06C62.9552 200.847 62.56 199.859 62.2674 199.137C62.1339 198.809 62.0261 198.548 61.944 198.338C61.9054 198.247 61.8745 198.153 61.8516 198.057Z" fill="#263238"/>
<path d="M68.2052 217.907C67.1489 216.153 65.6892 214.675 63.9468 213.595C62.2045 212.515 60.2294 211.864 58.1849 211.696C57.1582 211.624 55.8954 211.798 55.4334 212.72C54.9714 213.642 55.5874 214.768 56.2958 215.516C57.8009 217.086 59.7385 218.177 61.8643 218.651C63.9901 219.124 66.2089 218.96 68.2411 218.178" fill="#5F9EA0"/>
<path d="M59.7656 214.471C59.9833 214.461 60.2015 214.469 60.4176 214.497C60.6707 214.511 60.9227 214.54 61.1722 214.584C61.4648 214.64 61.8087 214.671 62.168 214.773C62.5697 214.858 62.9657 214.968 63.3538 215.101C63.8035 215.245 64.2441 215.416 64.6731 215.613C65.66 216.054 66.5934 216.605 67.4554 217.257C68.3074 217.923 69.0791 218.686 69.7551 219.53C70.047 219.897 70.3162 220.282 70.561 220.682C70.7824 221.027 70.9846 221.384 71.1668 221.752C71.321 222.051 71.4581 222.359 71.5774 222.674C71.6804 222.906 71.7678 223.146 71.8392 223.39C71.9153 223.595 71.972 223.805 72.0086 224.02C71.9522 224.02 71.7058 223.139 71.0128 221.829C70.8241 221.477 70.6167 221.135 70.3916 220.805C70.1415 220.417 69.8707 220.043 69.5806 219.683C68.2379 218.029 66.5257 216.71 64.5807 215.833C64.1341 215.639 63.708 215.454 63.2922 215.321C62.9105 215.183 62.5215 215.065 62.127 214.968C60.6896 214.574 59.7605 214.528 59.7656 214.471Z" fill="#263238"/>
<path d="M292.784 54.0064C291.275 54.3494 290.541 56.0851 290.397 57.6211C290.254 59.1571 290.449 60.7852 289.802 62.1881C289.073 63.7651 287.333 64.9324 287.276 66.6681C287.24 67.728 287.872 68.7161 287.841 69.7862C287.79 71.527 286.014 72.8582 285.916 74.5785C285.849 75.8329 286.686 77.031 286.486 78.27C286.332 79.2326 285.577 79.9904 285.259 80.912C284.628 82.7552 285.895 84.7776 287.543 85.8118C288.466 86.3907 289.505 86.7616 290.587 86.8985C291.669 87.0354 292.767 86.935 293.806 86.6044C294.845 86.2738 295.799 85.721 296.601 84.9845C297.404 84.248 298.035 83.3456 298.452 82.3404C299.365 80.1286 299.165 77.6352 298.965 75.2544C298.349 67.8269 297.89 60.3893 297.589 52.9414" fill="#263238"/>
<path d="M288.402 80.2103C288.713 80.0178 288.955 79.7331 289.095 79.3962C289.243 78.9955 289.303 78.5676 289.27 78.1418C289.214 77.5773 289.088 77.0218 288.895 76.4881C288.65 75.8171 288.558 75.0997 288.628 74.3889C288.757 73.5901 289.331 72.9348 289.85 72.2897C290.129 71.9739 290.362 71.6206 290.543 71.2401C290.724 70.8552 290.753 70.4166 290.625 70.0113C290.358 69.1767 289.537 68.5623 289.162 67.5946C288.986 67.1156 288.957 66.595 289.08 66.0996C289.196 65.6351 289.369 65.1864 289.593 64.7633C290.027 64.0011 290.371 63.192 290.62 62.3517C290.979 60.7287 290.81 59.1773 291.056 57.9025C291.193 56.8814 291.647 55.9288 292.355 55.1786C292.555 54.9766 292.783 54.8041 293.033 54.6666C293.118 54.622 293.21 54.5893 293.305 54.5693C292.983 54.746 292.687 54.9666 292.427 55.2247C291.771 55.9824 291.358 56.9186 291.241 57.9127C291.151 58.5918 291.103 59.2757 291.097 59.9607C291.096 60.7668 291.017 61.5709 290.861 62.362C290.616 63.2216 290.272 64.0498 289.835 64.8298C289.45 65.6337 289.095 66.5757 289.434 67.4564C289.773 68.337 290.589 68.9463 290.902 69.8884C291.044 70.3538 291.009 70.8548 290.805 71.2964C290.605 71.7152 290.345 72.1031 290.035 72.4484C289.521 73.0935 288.962 73.7079 288.839 74.4247C288.767 75.1046 288.847 75.7919 289.075 76.4369C289.258 76.9872 289.372 77.5582 289.414 78.1367C289.439 78.5822 289.36 79.0275 289.183 79.4372C289.072 79.694 288.895 79.9168 288.669 80.0823C288.589 80.1409 288.498 80.1843 288.402 80.2103Z" fill="#455A64"/>
<path d="M284.388 92.3496L279.768 105.662L260.446 106.686L257.223 113.854L277.397 115.902C278.35 116.292 279.372 116.485 280.403 116.47C281.433 116.456 282.449 116.232 283.391 115.815C284.332 115.397 285.179 114.793 285.88 114.04C286.58 113.287 287.121 112.399 287.468 111.432L292.853 96.4405L284.388 92.3496Z" fill="#B78876"/>
<path d="M324.332 217.959C324.332 217.959 310.174 173.097 309.872 171.643C309.6 170.322 315.005 138.041 315.693 129.659L296.561 130.371V130.56L288.43 131.011L295.709 222.72H304.512L303.64 192.282C309.071 206.818 316.853 221.635 316.853 221.635L324.332 217.959Z" fill="#B78876"/>
<path d="M303.998 203.203C303.998 202.927 303.891 193.004 303.891 193.004C303.783 193.598 300.518 184.392 300.595 183.834C300.636 183.542 303.998 203.203 303.998 203.203Z" fill="#A36957"/>
<path d="M300.226 179.472C300.181 179.654 300.162 179.842 300.17 180.03C300.141 180.541 300.141 181.054 300.17 181.566C300.17 181.888 300.216 182.236 300.257 182.615C300.298 182.994 300.355 183.394 300.442 183.808C300.624 184.714 300.866 185.607 301.166 186.481C301.802 188.34 302.511 189.963 303.008 191.14C303.245 191.652 303.45 192.139 303.624 192.528C303.687 192.709 303.774 192.881 303.881 193.04C303.854 192.863 303.802 192.69 303.727 192.528C303.609 192.19 303.429 191.703 303.214 191.104C302.762 189.906 302.084 188.268 301.453 186.43C301.154 185.568 300.909 184.69 300.719 183.798C300.627 183.388 300.565 182.994 300.509 182.626C300.452 182.257 300.411 181.909 300.385 181.602C300.324 180.967 300.298 180.455 300.283 180.096C300.292 179.887 300.273 179.676 300.226 179.472Z" fill="#A36957"/>
<path d="M295.544 223.186V222.362L304.563 221.968L304.588 225.373L304.034 225.408C301.555 225.552 291.432 225.972 289.779 225.475C287.926 224.922 295.544 223.186 295.544 223.186Z" fill="#455A64"/>
<path d="M304.706 225.168C304.655 225.171 304.603 225.171 304.552 225.168L304.11 225.199L302.478 225.27C301.102 225.327 299.198 225.378 297.098 225.388C294.999 225.398 293.089 225.363 291.708 225.322L290.081 225.255H289.634H289.48C289.532 225.251 289.583 225.251 289.634 225.255H290.081L291.713 225.286C293.089 225.286 294.994 225.327 297.093 225.316C299.193 225.306 301.102 225.27 302.478 225.235L304.11 225.194H304.706V225.168Z" fill="#263238"/>
<path d="M292.602 225.454C292.527 225.186 292.424 224.927 292.294 224.681C292.131 224.454 291.944 224.246 291.734 224.062C292.015 224.161 292.247 224.362 292.386 224.625C292.556 224.866 292.633 225.161 292.602 225.454Z" fill="#263238"/>
<path d="M295.262 224.108C295.262 224.108 295.113 223.97 294.98 223.765C294.846 223.56 294.764 223.381 294.79 223.365C294.816 223.35 294.939 223.498 295.072 223.703C295.206 223.908 295.288 224.092 295.262 224.108Z" fill="#263238"/>
<path d="M295.989 223.745C295.989 223.77 295.825 223.673 295.671 223.535C295.517 223.396 295.404 223.268 295.424 223.248C295.445 223.227 295.589 223.32 295.743 223.458C295.897 223.596 296.009 223.699 295.989 223.745Z" fill="#263238"/>
<path d="M296.447 222.874C296.296 222.929 296.135 222.95 295.975 222.935C295.818 222.956 295.658 222.94 295.508 222.889C295.657 222.834 295.817 222.813 295.975 222.828C296.134 222.806 296.296 222.822 296.447 222.874Z" fill="#263238"/>
<path d="M296.601 222.28C296.448 222.379 296.27 222.432 296.088 222.433C295.913 222.456 295.736 222.431 295.574 222.362C295.744 222.333 295.916 222.319 296.088 222.321C296.257 222.29 296.429 222.277 296.601 222.28Z" fill="#263238"/>
<path d="M294.956 223.325C294.764 223.361 294.568 223.361 294.376 223.325C294.168 223.3 293.964 223.255 293.765 223.192C293.642 223.158 293.523 223.11 293.411 223.048C293.342 223.008 293.291 222.944 293.267 222.869C293.258 222.826 293.261 222.782 293.276 222.741C293.292 222.7 293.319 222.665 293.354 222.639C293.496 222.57 293.652 222.534 293.81 222.532C293.968 222.531 294.124 222.564 294.268 222.631C294.411 222.697 294.538 222.794 294.639 222.915C294.741 223.036 294.814 223.177 294.853 223.33C294.863 223.381 294.863 223.433 294.853 223.484C294.795 223.299 294.705 223.125 294.586 222.972C294.45 222.818 294.271 222.708 294.073 222.654C293.846 222.581 293.599 222.599 293.385 222.705C293.298 222.772 293.334 222.879 293.431 222.941C293.536 222.997 293.646 223.044 293.76 223.079C293.953 223.142 294.15 223.192 294.35 223.228C294.735 223.309 294.956 223.309 294.956 223.325Z" fill="#263238"/>
<path d="M294.81 223.355C294.735 223.237 294.709 223.093 294.738 222.956C294.757 222.805 294.811 222.662 294.897 222.536C294.945 222.451 295.014 222.379 295.098 222.327C295.181 222.275 295.276 222.245 295.375 222.239C295.433 222.253 295.484 222.288 295.517 222.337C295.55 222.387 295.564 222.447 295.554 222.505C295.542 222.597 295.508 222.685 295.457 222.761C295.379 222.888 295.288 223.007 295.185 223.115C295.095 223.217 294.983 223.296 294.856 223.345C294.856 223.345 294.969 223.243 295.128 223.058C295.22 222.953 295.301 222.838 295.369 222.715C295.446 222.593 295.503 222.372 295.369 222.347C295.236 222.321 295.082 222.48 294.995 222.593C294.913 222.704 294.857 222.831 294.83 222.966C294.81 223.095 294.803 223.225 294.81 223.355Z" fill="#263238"/>
<path d="M304.517 222.721C304.517 222.721 304.261 222.721 303.855 222.746C303.327 222.809 302.827 223.019 302.413 223.351C301.998 223.683 301.685 224.125 301.509 224.625C301.376 225.009 301.366 225.27 301.345 225.265C301.325 225.26 301.345 225.199 301.345 225.086C301.358 224.921 301.389 224.758 301.437 224.6C301.601 224.076 301.915 223.611 302.341 223.263C302.772 222.922 303.296 222.717 303.845 222.675C304.015 222.654 304.188 222.654 304.358 222.675C304.413 222.683 304.467 222.698 304.517 222.721Z" fill="#263238"/>
<path d="M300.197 224.451C299.767 224.551 299.325 224.594 298.883 224.579C298.444 224.601 298.003 224.563 297.574 224.466C297.574 224.436 298.159 224.466 298.883 224.466C299.607 224.466 300.192 224.42 300.197 224.451Z" fill="#263238"/>
<path d="M301.956 224.436C301.956 224.436 301.91 224.558 301.848 224.692C301.787 224.825 301.756 224.947 301.725 224.942C301.694 224.937 301.679 224.804 301.751 224.645C301.823 224.487 301.935 224.41 301.956 224.436Z" fill="#263238"/>
<path d="M302.618 223.612C302.618 223.612 302.582 223.724 302.49 223.816C302.397 223.909 302.305 223.97 302.279 223.95C302.254 223.929 302.315 223.837 302.408 223.74C302.5 223.642 302.592 223.586 302.618 223.612Z" fill="#263238"/>
<path d="M303.56 223.232C303.56 223.263 303.437 223.263 303.303 223.309C303.17 223.355 303.077 223.427 303.052 223.407C303.026 223.386 303.103 223.263 303.267 223.207C303.432 223.151 303.57 223.202 303.56 223.232Z" fill="#263238"/>
<path d="M304.233 223.048C304.233 223.079 304.197 223.125 304.12 223.151C304.043 223.176 303.976 223.182 303.966 223.151C303.956 223.12 304.007 223.074 304.079 223.048C304.151 223.023 304.223 223.023 304.233 223.048Z" fill="#263238"/>
<path d="M316.67 222.393L316.331 221.64L324.395 217.601L325.817 220.673L325.304 220.934C323.102 222.075 314.041 226.591 312.327 226.817C310.433 227.088 316.67 222.393 316.67 222.393Z" fill="#455A64"/>
<path d="M325.85 220.452L325.716 220.534L325.321 220.739L323.863 221.476C322.631 222.085 320.916 222.91 319.002 223.78C317.087 224.651 315.331 225.393 314.058 225.92L312.544 226.53L312.133 226.688C312.086 226.71 312.036 226.726 311.984 226.734L312.123 226.668L312.529 226.489L314.027 225.849C315.295 225.306 317.041 224.548 318.955 223.678C320.87 222.807 322.59 221.998 323.832 221.404L325.3 220.703L325.706 220.514L325.85 220.452Z" fill="#263238"/>
<path d="M314.944 225.659C314.766 225.443 314.564 225.247 314.343 225.075C314.102 224.933 313.846 224.82 313.578 224.737C313.873 224.733 314.162 224.82 314.406 224.984C314.65 225.149 314.838 225.385 314.944 225.659Z" fill="#263238"/>
<path d="M316.795 223.345C316.795 223.345 316.6 223.284 316.395 223.151C316.189 223.017 316.04 222.889 316.056 222.864C316.071 222.838 316.251 222.925 316.456 223.058C316.661 223.192 316.81 223.32 316.795 223.345Z" fill="#263238"/>
<path d="M317.297 222.72C317.297 222.746 317.122 222.72 316.922 222.659C316.722 222.597 316.573 222.52 316.578 222.49C316.708 222.478 316.839 222.499 316.958 222.551C317.153 222.597 317.307 222.669 317.297 222.72Z" fill="#263238"/>
<path d="M317.372 221.737C317.372 221.768 317.202 221.876 316.966 221.988C316.83 222.07 316.678 222.12 316.52 222.137C316.52 222.106 316.689 221.998 316.92 221.886C317.058 221.805 317.212 221.754 317.372 221.737Z" fill="#263238"/>
<path d="M317.269 221.133C317.269 221.133 317.12 221.338 316.853 221.486C316.702 221.589 316.523 221.642 316.34 221.64C316.487 221.541 316.643 221.457 316.807 221.389C317.059 221.251 317.243 221.107 317.269 221.133Z" fill="#263238"/>
<path d="M316.216 222.762C316.06 222.869 315.886 222.948 315.703 222.997C315.501 223.056 315.295 223.098 315.087 223.125C314.96 223.141 314.833 223.141 314.707 223.125C314.627 223.119 314.554 223.082 314.501 223.023C314.478 222.986 314.465 222.943 314.465 222.9C314.465 222.856 314.478 222.813 314.501 222.777C314.657 222.588 314.874 222.46 315.115 222.413C315.356 222.366 315.605 222.404 315.821 222.521C315.948 222.587 316.06 222.68 316.149 222.792C316.211 222.874 316.236 222.925 316.231 222.931C316.105 222.79 315.953 222.673 315.785 222.587C315.604 222.503 315.402 222.473 315.205 222.5C315.087 222.513 314.973 222.548 314.869 222.604C314.765 222.66 314.673 222.736 314.599 222.828C314.542 222.925 314.599 223.007 314.738 223.023C314.855 223.033 314.974 223.033 315.092 223.023C315.296 223.002 315.499 222.966 315.697 222.915C315.99 222.833 316.216 222.746 316.216 222.762Z" fill="#263238"/>
<path d="M316.071 222.848C315.956 222.767 315.876 222.647 315.845 222.51C315.8 222.366 315.789 222.214 315.814 222.065C315.823 221.967 315.857 221.872 315.912 221.791C315.968 221.709 316.044 221.643 316.132 221.599C316.261 221.558 316.558 221.819 316.579 221.916C316.599 222.014 316.43 221.963 316.42 222.044C316.403 222.19 316.369 222.333 316.317 222.469C316.28 222.601 316.209 222.721 316.112 222.818C316.112 222.818 316.173 222.679 316.245 222.449C316.282 222.313 316.308 222.174 316.322 222.034C316.322 221.891 316.322 221.671 316.158 221.707C315.994 221.742 315.947 221.942 315.911 222.085C315.88 222.218 315.88 222.357 315.911 222.49C315.954 222.614 316.007 222.734 316.071 222.848Z" fill="#263238"/>
<path d="M324.668 218.297C324.668 218.297 324.421 218.384 324.072 218.594C323.616 218.866 323.244 219.26 322.999 219.73C322.762 220.206 322.66 220.737 322.707 221.266C322.737 221.676 322.835 221.912 322.814 221.922C322.781 221.871 322.755 221.816 322.737 221.758C322.687 221.601 322.651 221.44 322.63 221.277C322.556 220.731 322.651 220.177 322.901 219.687C323.152 219.196 323.546 218.794 324.031 218.532C324.176 218.452 324.329 218.387 324.488 218.338C324.546 218.315 324.606 218.302 324.668 218.297Z" fill="#263238"/>
<path d="M321.442 221.645C320.718 222.147 319.909 222.515 319.055 222.73C319.055 222.7 319.594 222.495 320.251 222.218C320.908 221.942 321.426 221.619 321.442 221.645Z" fill="#263238"/>
<path d="M323.036 220.913C323.062 220.913 323.036 221.041 323.036 221.19C323.036 221.338 323.036 221.461 323.036 221.471C323.036 221.482 322.934 221.359 322.934 221.19C322.934 221.021 323.011 220.898 323.036 220.913Z" fill="#263238"/>
<path d="M323.3 219.889C323.325 219.889 323.3 220.007 323.269 220.13C323.238 220.253 323.161 220.345 323.136 220.335C323.11 220.325 323.136 220.217 323.166 220.094C323.197 219.971 323.269 219.879 323.3 219.889Z" fill="#263238"/>
<path d="M324.009 219.136C324.009 219.167 323.906 219.213 323.803 219.31C323.701 219.407 323.644 219.51 323.618 219.5C323.593 219.489 323.619 219.346 323.731 219.228C323.844 219.111 324.004 219.136 324.009 219.136Z" fill="#263238"/>
<path d="M324.548 218.716C324.548 218.716 324.548 218.798 324.486 218.855C324.425 218.911 324.368 218.942 324.348 218.921C324.327 218.901 324.348 218.839 324.409 218.783C324.471 218.727 324.527 218.696 324.548 218.716Z" fill="#263238"/>
<path d="M314.448 114.504L312.95 96.2259L320.65 96.3641C320.65 96.3641 321.004 93.6659 319.869 87.7574C319.171 84.1324 316.753 81.9104 314.038 80.5894V80.5382L313.802 80.4768C311.566 79.4758 309.179 78.8545 306.738 78.6387L297.267 78.7462C297.267 78.7462 290.034 77.8246 286.046 82.9907C284.506 84.9721 279.727 98.535 279.727 98.535L291.097 103.993L290.034 96.4716C290.034 96.4716 290.25 101.924 290.948 103.64C291.333 104.602 292.488 107.592 292.488 107.592L292.339 116.614L314.448 114.504Z" fill="#5F9EA0"/>
<path d="M309.227 93.5731L307.82 99.543H321.08L320.207 89.5283L309.227 93.5731Z" fill="#5F9EA0"/>
<path d="M321.082 99.5436C320.503 99.4371 319.916 99.3789 319.327 99.3695C318.238 99.308 316.76 99.2517 315.076 99.262C313.393 99.2722 311.909 99.3439 310.821 99.4258C310.232 99.4462 309.646 99.5182 309.07 99.6408C309.658 99.6875 310.248 99.6875 310.836 99.6408C311.919 99.605 313.403 99.564 315.076 99.5589C316.75 99.5538 318.233 99.5589 319.322 99.5845C319.909 99.6173 320.497 99.6036 321.082 99.5436Z" fill="#263238"/>
<path d="M308.654 89.4824C308.591 89.9919 308.577 90.5063 308.613 91.0184C308.613 91.9554 308.654 93.2456 308.711 94.6741C308.767 96.1026 308.834 97.3979 308.896 98.3298C308.898 98.846 308.953 99.3606 309.06 99.8658C309.123 99.3563 309.137 98.8419 309.101 98.3298C309.101 97.3928 309.06 96.0975 309.003 94.669C308.947 93.2405 308.88 91.9503 308.819 91.0133C308.816 90.4987 308.761 89.9858 308.654 89.4824Z" fill="#263238"/>
<g opacity="0.3">
<path d="M296.59 94.6543C296.59 94.6543 299.536 116.45 307.822 115.872C307.822 115.872 311.554 115.831 312.75 113.358V112.385L296.59 94.6543Z" fill="black"/>
</g>
<g opacity="0.3">
<path d="M308.653 90.7676L307.066 101.453L308.093 103.327L309.787 99.661L309.068 99.6405L308.653 90.7676Z" fill="black"/>
</g>
<path d="M292.443 107.603C292.344 107.443 292.268 107.271 292.217 107.091C292.089 106.758 291.919 106.271 291.704 105.662C291.19 104.075 290.766 102.459 290.436 100.824C290.106 99.1873 289.897 97.5292 289.809 95.8624C289.779 95.2224 289.768 94.7053 289.779 94.3264C289.765 94.1413 289.778 93.9553 289.815 93.7734C289.856 93.7734 289.886 94.5619 289.994 95.8214C290.139 97.4731 290.373 99.1156 290.697 100.742C291.077 102.646 291.534 104.351 291.883 105.575C292.048 106.143 292.186 106.63 292.299 107.029C292.37 107.214 292.418 107.406 292.443 107.603Z" fill="#263238"/>
<path d="M295.79 113.864C295.718 113.864 295.513 113.419 295.333 112.84C295.179 112.486 295.11 112.1 295.133 111.714C295.36 112.027 295.516 112.385 295.59 112.763C295.737 113.111 295.806 113.487 295.79 113.864Z" fill="#263238"/>
<path d="M296.932 113.782C296.544 112.755 296.528 111.625 296.886 110.587C296.926 111.119 296.926 111.653 296.886 112.184C296.942 112.715 296.958 113.249 296.932 113.782Z" fill="#263238"/>
<path d="M291.328 104.243C291.292 104.305 288.648 103.056 285.42 101.448C282.191 99.8402 279.598 98.4783 279.629 98.4117C279.66 98.3452 282.309 99.5995 285.538 101.207C288.767 102.815 291.359 104.177 291.328 104.243Z" fill="#263238"/>
<path d="M295.991 80.5127C296.047 77.922 296.063 75.1981 296.063 75.1981C296.063 75.1981 292.731 74.5581 292.151 70.1856C291.597 66.018 293.147 56.2336 293.147 56.2336C298.013 54.4007 303.07 56.0647 306.401 59.9968L305.575 81.0247C305.477 83.4669 303.295 85.3306 300.677 85.2077C298.029 85.0797 295.934 82.9805 295.991 80.5127Z" fill="#B78876"/>
<path d="M292.887 64.0517C292.882 64.1953 292.933 64.3351 293.029 64.4426C293.124 64.5502 293.257 64.6173 293.4 64.6303C293.473 64.6373 293.547 64.6295 293.616 64.6072C293.686 64.585 293.751 64.5488 293.806 64.5009C293.861 64.453 293.906 64.3943 293.938 64.3286C293.97 64.2628 293.988 64.1912 293.991 64.1183C293.995 63.9753 293.944 63.8361 293.849 63.7294C293.753 63.6226 293.62 63.5566 293.477 63.5449C293.404 63.5338 293.33 63.5386 293.258 63.559C293.187 63.5794 293.121 63.6149 293.065 63.663C293.009 63.7112 292.964 63.7708 292.934 63.8379C292.903 63.905 292.887 63.9779 292.887 64.0517Z" fill="#263238"/>
<path d="M292.685 63.3199C292.752 63.3915 293.198 63.0895 293.789 63.1099C294.379 63.1304 294.815 63.4376 294.887 63.3659C294.959 63.2943 294.857 63.2021 294.667 63.0536C294.409 62.8679 294.101 62.766 293.784 62.7618C293.471 62.7499 293.163 62.8397 292.906 63.0178C292.716 63.156 292.649 63.284 292.685 63.3199Z" fill="#263238"/>
<path d="M299.004 64.4306C298.999 64.5745 299.049 64.715 299.144 64.8228C299.24 64.9307 299.374 64.9974 299.518 65.0092C299.591 65.0203 299.666 65.0154 299.738 64.9947C299.809 64.974 299.875 64.9381 299.932 64.8894C299.988 64.8407 300.033 64.7804 300.063 64.7126C300.094 64.6449 300.109 64.5714 300.108 64.4972C300.114 64.3539 300.063 64.214 299.968 64.107C299.872 64 299.738 63.9343 299.595 63.9238C299.522 63.9127 299.447 63.9175 299.376 63.9379C299.305 63.9583 299.239 63.9938 299.183 64.0419C299.127 64.0901 299.082 64.1497 299.051 64.2168C299.02 64.2839 299.004 64.3569 299.004 64.4306Z" fill="#263238"/>
<path d="M298.96 63.6984C299.027 63.7752 299.474 63.4731 300.064 63.4885C300.654 63.5038 301.091 63.8162 301.168 63.7445C301.245 63.6728 301.132 63.5806 300.942 63.4373C300.689 63.2467 300.381 63.1425 300.064 63.1403C299.75 63.1309 299.441 63.2205 299.181 63.3963C298.996 63.5346 298.93 63.6626 298.96 63.6984Z" fill="#263238"/>
<path d="M296.66 68.0967C296.345 67.9887 296.017 67.9216 295.685 67.8971C295.531 67.8971 295.387 67.8407 295.366 67.7383C295.354 67.5783 295.394 67.4185 295.479 67.2827L295.962 66.1307C296.411 65.1516 296.78 64.1379 297.066 63.0996C296.54 64.0404 296.09 65.0214 295.721 66.0334C295.562 66.4379 295.402 66.827 295.254 67.1956C295.154 67.3785 295.121 67.5903 295.161 67.7947C295.181 67.8502 295.214 67.9003 295.257 67.9411C295.299 67.982 295.351 68.0124 295.407 68.0302C295.493 68.0548 295.581 68.0685 295.669 68.0711C295.997 68.1215 296.33 68.1301 296.66 68.0967Z" fill="#263238"/>
<path d="M296.062 75.198C298.116 75.28 300.153 74.7927 301.945 73.79C301.945 73.79 300.451 76.7443 296.093 76.2272L296.062 75.198Z" fill="#A36957"/>
<path d="M300.892 61.9522C300.769 62.1416 300.307 62.0597 299.793 61.9522C299.28 61.8447 298.828 61.7576 298.767 61.5375C298.705 61.3173 299.244 61.0255 299.932 61.1637C300.62 61.3019 301.015 61.7883 300.892 61.9522Z" fill="#263238"/>
<path d="M294.899 61.0817C294.853 61.3018 294.36 61.3838 293.796 61.4094C293.231 61.435 292.733 61.4094 292.666 61.1841C292.6 60.9588 293.077 60.6414 293.76 60.6106C294.443 60.5799 294.946 60.8718 294.899 61.0817Z" fill="#263238"/>
<path d="M296.67 69.5347C296.605 69.5193 296.537 69.5261 296.477 69.554C296.416 69.5818 296.367 69.6291 296.337 69.6883C296.307 69.7405 296.292 69.7994 296.293 69.8592C296.294 69.919 296.31 69.9776 296.34 70.0292C296.371 70.0807 296.414 70.1235 296.466 70.1532C296.518 70.1829 296.577 70.1985 296.637 70.1985C296.697 70.1985 296.756 70.1829 296.808 70.1532C296.86 70.1235 296.903 70.0807 296.933 70.0292C296.964 69.9776 296.98 69.919 296.981 69.8592C296.981 69.7994 296.966 69.7405 296.937 69.6883C296.9 69.6195 296.841 69.5653 296.769 69.5346C296.697 69.5039 296.617 69.4985 296.542 69.5193" fill="#A36957"/>
<path d="M296.812 69.5962C296.812 69.5706 296.715 69.4222 296.453 69.4785C296.375 69.4988 296.303 69.5394 296.246 69.596C296.188 69.6526 296.146 69.7233 296.125 69.801C296.097 69.8991 296.099 70.003 296.131 70.0997C296.163 70.1964 296.224 70.2814 296.304 70.3438C296.39 70.4021 296.491 70.4333 296.594 70.4333C296.698 70.4333 296.799 70.4021 296.884 70.3438C296.953 70.2979 297.006 70.2335 297.039 70.1582C297.072 70.0829 297.082 69.9998 297.069 69.9188C297.018 69.6474 296.818 69.6065 296.807 69.6423C296.797 69.6782 296.879 69.7703 296.864 69.929C296.86 69.9683 296.847 70.006 296.826 70.0391C296.804 70.0721 296.775 70.0994 296.741 70.1185C296.701 70.1414 296.656 70.1535 296.61 70.1535C296.564 70.1535 296.519 70.1414 296.479 70.1185C296.443 70.0882 296.416 70.0491 296.399 70.0052C296.383 69.9612 296.379 69.9138 296.386 69.8676C296.392 69.8271 296.408 69.7887 296.432 69.7557C296.456 69.7227 296.488 69.6961 296.525 69.6782C296.669 69.5911 296.797 69.6321 296.812 69.5962Z" fill="#263238"/>
<path d="M304.407 65.1317C303.893 66.3298 302.62 67.2565 302.795 68.5519C302.908 69.3864 303.447 70.1647 303.344 71.0043C303.211 72.0283 302.117 72.7861 301.958 73.8357C301.768 75.1208 303.026 76.1192 303.524 77.3173C304.258 79.0683 303.323 81.0344 303.298 82.9493C303.328 84.4031 303.886 85.7967 304.867 86.8718C305.849 87.9469 307.188 88.6307 308.636 88.7963C309.807 88.8936 311.126 88.5403 311.716 87.5368C312.23 86.7074 312.117 85.6527 312.415 84.7362C312.866 83.364 314.175 82.3605 314.468 80.9474C314.981 78.3874 311.85 76.237 312.055 73.6155C312.143 72.4943 312.836 71.5215 313.128 70.436C313.314 69.7227 313.329 68.9755 313.17 68.2555C313.011 67.5356 312.684 66.8634 312.214 66.2939C311.439 65.3775 310.32 64.7579 309.648 63.7749C308.903 62.6434 308.878 61.2149 308.621 59.8786C308.276 58.2543 307.524 56.7437 306.436 55.488C305.347 54.2322 303.956 53.2724 302.395 52.6981C300.833 52.1237 299.15 51.9537 297.505 52.204C295.859 52.4542 294.304 53.1164 292.985 54.1288C292.985 54.1288 292.241 57.5029 295.716 58.0712C299.191 58.6395 300.798 57.9842 301.188 59.3717C301.096 59.9433 301.141 60.5285 301.32 61.0793C301.498 61.6302 301.805 62.1311 302.215 62.541C303.631 64.0667 304.982 63.7391 304.407 65.1317Z" fill="#263238"/>
<path d="M309.926 88.5351C309.819 88.5387 309.712 88.5284 309.608 88.5044C309.307 88.4511 309.011 88.3706 308.725 88.2637C307.665 87.8584 306.735 87.1735 306.035 86.2823C305.544 85.6673 305.159 84.9752 304.896 84.2343C304.596 83.3905 304.438 82.5029 304.429 81.6077C304.418 80.621 304.542 79.6374 304.798 78.6842C304.938 78.1808 305.11 77.6866 305.311 77.2045C305.523 76.7247 305.71 76.2342 305.871 75.7351C306.012 75.2432 306.012 74.7217 305.871 74.2298C305.721 73.7456 305.527 73.276 305.291 72.8269C305.053 72.3816 304.852 71.9175 304.69 71.4394C304.532 70.9662 304.495 70.4611 304.583 69.97C304.788 69.0074 305.373 68.2906 305.722 67.5584C306.055 66.8807 306.213 66.1313 306.184 65.3773C306.137 64.274 305.736 63.2148 305.039 62.3565C304.77 62.0559 304.48 61.7736 304.172 61.5117C304.532 61.7134 304.853 61.9769 305.122 62.29C305.869 63.147 306.31 64.2277 306.374 65.362C306.417 66.1488 306.261 66.9337 305.922 67.6455C305.568 68.4032 304.998 69.1252 304.824 70.0058C304.649 70.8864 305.065 71.8132 305.522 72.704C305.771 73.166 305.974 73.6513 306.128 74.153C306.279 74.6887 306.279 75.2557 306.128 75.7914C305.957 76.3079 305.76 76.8155 305.537 77.312C305.335 77.7836 305.164 78.2676 305.024 78.761C304.769 79.6901 304.643 80.6496 304.649 81.6128C304.633 83.2533 305.164 84.8525 306.158 86.1594C306.823 87.0395 307.708 87.7299 308.725 88.1613C309.49 88.4583 309.931 88.5044 309.926 88.5351Z" fill="#455A64"/>
<path d="M309.413 62.7003C309.163 62.7649 308.907 62.8027 308.648 62.813C307.944 62.8881 307.233 62.8692 306.533 62.7567C306.003 62.6784 305.494 62.4954 305.036 62.2185C304.578 61.9415 304.179 61.5762 303.864 61.1439C303.571 60.5969 303.337 60.0201 303.166 59.4235C302.947 58.8276 302.535 58.3214 301.996 57.9848C300.871 57.2731 299.552 57.1861 298.402 57.0479C297.342 56.9951 296.312 56.6745 295.41 56.116C294.805 55.7166 294.369 55.11 294.183 54.4111C294.138 54.2216 294.126 54.026 294.147 53.8325C294.147 53.6994 294.178 53.6328 294.188 53.6328C294.194 53.8864 294.227 54.1387 294.285 54.3855C294.498 55.039 294.934 55.5972 295.517 55.9624C296.403 56.4779 297.403 56.7663 298.428 56.8021C299.578 56.9352 300.943 57.0018 302.144 57.7595C302.732 58.1277 303.18 58.6808 303.418 59.3314C303.578 59.9129 303.8 60.4758 304.08 61.0107C304.678 61.8288 305.573 62.3803 306.575 62.5467C307.251 62.6722 307.94 62.7186 308.628 62.685C308.89 62.6703 309.152 62.6754 309.413 62.7003Z" fill="#455A64"/>
<path d="M308.139 58.5318C307.946 58.5963 307.742 58.6258 307.539 58.6188C306.582 58.6253 305.66 58.2577 304.972 57.5948C304.658 57.2808 304.39 56.9239 304.176 56.535C303.714 55.7567 303.268 54.7993 302.308 54.3999C301.383 54.0905 300.398 54.0045 299.433 54.1491C298.619 54.2631 297.793 54.2631 296.979 54.1491C296.413 54.0707 295.88 53.8367 295.439 53.4732C295.326 53.3742 295.226 53.2606 295.142 53.1353C295.08 53.0466 295.054 53.0005 295.065 52.9971C295.065 52.9971 295.208 53.1609 295.506 53.3913C295.945 53.7027 296.455 53.8998 296.99 53.9648C297.789 54.0546 298.596 54.0409 299.392 53.9238C300.399 53.7598 301.431 53.8478 302.395 54.1798C302.891 54.402 303.315 54.7563 303.622 55.2038C303.893 55.6029 304.141 56.0166 304.366 56.4428C304.569 56.815 304.819 57.1592 305.111 57.4668C305.368 57.727 305.664 57.9463 305.988 58.1171C306.466 58.3657 306.991 58.5106 307.528 58.542C307.924 58.5369 308.134 58.5062 308.139 58.5318Z" fill="#455A64"/>
<path d="M311.456 87.4297C311.43 87.4297 311.595 87.0917 311.785 86.4569C312.042 85.5656 312.156 84.6394 312.124 83.7125C312.086 82.3405 311.711 80.9988 311.03 79.806C310.655 79.1353 310.086 78.5362 309.618 77.7989C309.371 77.4234 309.197 77.0048 309.105 76.565C309.024 76.1021 309.063 75.6263 309.218 75.1826C309.506 74.2969 310.019 73.5545 310.132 72.7404C310.245 71.9263 309.855 71.2044 309.464 70.5234C309.263 70.1921 309.08 69.8502 308.915 69.4994C308.765 69.1592 308.666 68.7985 308.623 68.4293C308.574 67.7787 308.595 67.1249 308.684 66.4786C308.848 65.5651 308.763 64.6243 308.438 63.7548C308.292 63.4502 308.086 63.1784 307.832 62.9561C307.832 62.9561 307.858 62.9561 307.894 62.9868C307.954 63.0169 308.01 63.0567 308.058 63.1045C308.259 63.2713 308.422 63.4791 308.535 63.7138C308.907 64.5908 309.022 65.5544 308.869 66.494C308.798 67.1282 308.79 67.7678 308.843 68.4037C308.97 69.1191 309.254 69.7975 309.675 70.3903C309.894 70.7363 310.081 71.1013 310.234 71.4809C310.396 71.8906 310.449 72.335 310.388 72.7711C310.265 73.6671 309.736 74.4249 309.47 75.2594C309.329 75.6621 309.292 76.0933 309.362 76.5138C309.451 76.9206 309.615 77.3076 309.844 77.6556C310.281 78.3673 310.871 78.9765 311.246 79.678C311.933 80.9111 312.301 82.2964 312.313 83.7074C312.33 84.6519 312.184 85.5922 311.882 86.4876C311.8 86.7299 311.701 86.966 311.584 87.1941C311.553 87.2783 311.51 87.3576 311.456 87.4297Z" fill="#455A64"/>
<path d="M290.548 182.83C290.548 182.83 287.088 150.144 286.888 138.066C286.688 125.988 292.334 114.069 292.334 114.069L314.449 113.152C314.449 113.152 318.822 125.405 318.899 130.56C319.053 140.565 316.235 150.441 316.559 158.377L317.298 182.851" fill="#263238"/>
<path d="M318.04 142.644C317.925 142.743 317.803 142.836 317.676 142.92L317.476 143.058L317.26 143.268C317.07 143.432 316.898 143.616 316.747 143.816C316.033 144.686 315.515 146.248 315.017 148.189C314.762 149.265 314.387 150.309 313.898 151.301C313.749 151.573 313.577 151.832 313.384 152.075C313.292 152.197 313.205 152.331 313.097 152.448L312.758 152.786C312.273 153.239 311.709 153.599 311.095 153.851C309.858 154.394 308.456 154.65 307.183 155.29C305.879 155.99 304.871 157.135 304.345 158.516C303.718 159.903 303.318 161.357 302.805 162.719C302.291 164.081 301.598 165.381 300.495 166.211C299.439 166.942 298.186 167.337 296.901 167.342C295.71 167.414 294.607 167.378 293.616 167.527C292.724 167.619 291.872 167.94 291.142 168.459C290.608 168.874 290.209 169.437 289.992 170.077C289.854 170.48 289.824 170.912 289.905 171.331C289.966 171.618 290.043 171.756 290.028 171.761C289.945 171.632 289.886 171.49 289.853 171.341C289.745 170.914 289.759 170.465 289.894 170.046C290.098 169.374 290.501 168.78 291.049 168.341C291.798 167.777 292.684 167.424 293.616 167.317C294.643 167.153 295.736 167.173 296.912 167.097C298.159 167.089 299.374 166.703 300.397 165.991C301.434 165.087 302.2 163.913 302.61 162.601C303.123 161.255 303.498 159.796 304.15 158.382C304.702 156.945 305.755 155.754 307.117 155.029C308.441 154.373 309.842 154.123 311.049 153.6C311.639 153.357 312.182 153.01 312.65 152.576L312.974 152.254C313.076 152.141 313.164 152.013 313.256 151.895C313.437 151.663 313.597 151.414 313.733 151.153C314.225 150.174 314.606 149.144 314.868 148.081C315.381 146.146 315.941 144.564 316.695 143.688C316.846 143.489 317.018 143.306 317.209 143.145C317.291 143.074 317.363 143.002 317.435 142.946L317.645 142.813C317.773 142.748 317.905 142.692 318.04 142.644Z" fill="#FAFAFA"/>
<path d="M318.176 127.52C318.199 127.663 318.21 127.809 318.207 127.955C318.205 128.377 318.153 128.799 318.053 129.209C317.611 130.707 316.742 132.044 315.553 133.059C314.768 133.779 313.907 134.413 312.986 134.949C311.995 135.532 310.902 136.09 309.778 136.725C308.597 137.357 307.497 138.129 306.503 139.024C305.482 140.034 304.642 141.209 304.018 142.501C303.389 143.809 302.67 145.074 301.867 146.284C301.459 146.849 300.987 147.364 300.461 147.82C299.941 148.261 299.371 148.637 298.762 148.942C297.659 149.482 296.509 149.922 295.327 150.257C294.229 150.595 293.202 150.897 292.309 151.281C291.498 151.603 290.727 152.015 290.009 152.51C289.469 152.884 288.984 153.33 288.567 153.836C288.218 154.275 287.926 154.757 287.699 155.27C287.736 155.128 287.784 154.989 287.843 154.855C288.011 154.468 288.227 154.104 288.485 153.77C288.894 153.242 289.378 152.776 289.922 152.387C290.643 151.875 291.419 151.445 292.237 151.107C293.226 150.705 294.235 150.355 295.261 150.058C296.426 149.715 297.559 149.273 298.649 148.737C299.226 148.436 299.769 148.072 300.266 147.651C300.772 147.202 301.227 146.697 301.621 146.146C302.413 144.941 303.124 143.686 303.751 142.388C304.395 141.068 305.264 139.869 306.318 138.845C307.33 137.936 308.451 137.155 309.654 136.521C310.784 135.891 311.877 135.343 312.868 134.764C313.786 134.246 314.647 133.632 315.435 132.931C316.614 131.947 317.49 130.649 317.96 129.189C318.092 128.642 318.164 128.082 318.176 127.52Z" fill="#FAFAFA"/>
<path d="M287 139.776C286.979 139.745 286.964 139.71 286.953 139.674C286.915 139.575 286.882 139.474 286.856 139.372C286.768 138.965 286.786 138.542 286.907 138.143C287.195 137.119 288.329 135.803 290.157 135.153C291.07 134.841 292.102 134.641 293.18 134.375C294.075 134.227 294.922 133.868 295.65 133.327C296.378 132.786 296.965 132.08 297.364 131.267C297.656 130.67 297.911 130.056 298.129 129.429C298.53 128.066 299.076 126.75 299.756 125.502C300.536 124.275 301.719 123.358 303.103 122.906C304.362 122.499 305.646 122.174 306.948 121.933C308.109 121.711 309.234 121.328 310.29 120.796C311.184 120.287 311.955 119.588 312.548 118.748C313.425 117.537 313.925 116.094 313.986 114.601C313.988 114.053 313.952 113.506 313.878 112.963C314.033 113.495 314.104 114.048 314.088 114.601C314.068 116.127 313.585 117.61 312.702 118.856C312.101 119.723 311.314 120.445 310.397 120.971C309.328 121.522 308.185 121.92 307.004 122.153C305.715 122.397 304.443 122.724 303.195 123.131C301.867 123.565 300.731 124.447 299.982 125.625C299.312 126.855 298.774 128.153 298.375 129.495C298.153 130.134 297.891 130.758 297.59 131.364C297.286 131.946 296.898 132.481 296.44 132.951C295.538 133.771 294.429 134.329 293.231 134.564C292.143 134.841 291.117 135.015 290.218 135.312C288.437 135.916 287.308 137.16 287.005 138.153C286.88 138.536 286.847 138.943 286.907 139.341C286.951 139.483 286.982 139.629 287 139.776Z" fill="#FAFAFA"/>
<path d="M316.278 167.425C316.299 167.531 316.311 167.639 316.314 167.747C316.324 168.058 316.291 168.368 316.216 168.669C315.894 169.779 315.211 170.751 314.276 171.434C313.651 171.93 312.96 172.337 312.222 172.642C311.437 172.975 310.57 173.241 309.656 173.523C308.707 173.799 307.779 174.141 306.879 174.547C305.943 174.993 305.104 175.617 304.41 176.385C303.66 177.163 303.013 178.018 302.295 178.776C301.607 179.51 300.809 180.132 299.928 180.619C298.43 181.435 296.753 181.871 295.046 181.889C293.903 181.901 292.767 181.712 291.689 181.331C291.397 181.23 291.112 181.109 290.837 180.967C290.737 180.926 290.642 180.873 290.555 180.808C290.658 180.837 290.759 180.873 290.858 180.916C291.139 181.036 291.427 181.142 291.72 181.233C292.796 181.57 293.919 181.727 295.046 181.699C296.716 181.656 298.35 181.212 299.81 180.404C300.663 179.922 301.437 179.313 302.105 178.597C302.808 177.849 303.45 176.999 304.214 176.2C304.95 175.404 305.836 174.76 306.822 174.306C307.736 173.899 308.678 173.557 309.64 173.282C310.544 173.006 311.406 172.77 312.207 172.432C312.923 172.14 313.597 171.752 314.209 171.28C315.129 170.632 315.815 169.706 316.165 168.638C316.241 168.238 316.278 167.832 316.278 167.425Z" fill="#FAFAFA"/>
<path d="M313.754 99.5639L309.473 99.6663C309.308 100.527 308.981 101.349 308.508 102.088C304.915 95.8929 302.158 87.5985 301.47 84.2654C301.573 83.9326 301.691 83.5639 301.84 83.1543C301.947 82.5322 301.927 81.895 301.783 81.2804C301.66 80.538 301.455 79.5396 301.393 79.0788C301.203 77.6657 300.972 77.4455 300.48 77.6043C299.812 77.8244 300.757 81.562 300.208 81.6439C299.91 81.6849 299.55 80.4049 299.448 79.3553C299.345 78.3057 298.863 75.0699 298.519 74.8548C298.005 74.5374 297.559 75.0391 297.692 76.0836C297.826 77.1281 297.985 80.3025 297.43 80.3486C296.876 80.3947 296.404 75.0289 296.404 75.0289C296.404 75.0289 296.491 73.8615 295.844 73.8871C294.684 73.9383 295.593 79.5908 295.69 80.1284C295.762 80.4971 295.177 80.5687 295.105 80.1643C295.033 79.7598 294.766 74.8036 293.565 74.8446C292.651 74.8446 294.268 79.4987 293.755 80.5739C293.242 81.6491 292.893 76.9284 292.025 76.9899C291.702 77.0155 291.46 77.0769 292.056 79.7444C292.626 81.8638 293.312 83.9508 294.109 85.9959C295.485 93.3943 301.522 117.064 310.982 112.733C316.28 110.306 318.426 99.5639 318.426 99.5639H313.754Z" fill="#B78876"/>
<path d="M295.506 80.4872C295.546 80.4973 295.589 80.4973 295.629 80.4872C295.691 80.4683 295.744 80.428 295.779 80.3736C295.814 80.3192 295.828 80.2541 295.819 80.1902C295.819 79.8728 295.758 79.4119 295.686 78.8129C295.565 78.0909 295.487 77.3627 295.45 76.6318C295.45 76.2017 295.45 75.7409 295.475 75.2545C295.483 75.0072 295.509 74.7608 295.552 74.5172C295.593 74.2868 295.747 74.0052 295.927 74.0513H295.958C296.137 74.0513 296.281 74.1844 296.338 74.3995C296.401 74.6223 296.425 74.8544 296.409 75.0856C296.515 76.5194 296.699 77.9466 296.959 79.3608C297 79.5502 297.041 79.7448 297.102 79.9393C297.13 80.0389 297.164 80.1364 297.205 80.2312C297.231 80.2839 297.261 80.3352 297.292 80.3848C297.315 80.4222 297.347 80.4537 297.384 80.4768C297.422 80.4999 297.464 80.514 297.508 80.5179C297.583 80.5097 297.653 80.4795 297.711 80.4312C297.769 80.3829 297.811 80.3186 297.831 80.2465C297.876 80.1464 297.908 80.0416 297.929 79.9342C297.969 79.7278 297.996 79.5192 298.011 79.3095C298.058 78.4564 298.046 77.6011 297.975 76.7496C297.91 76.3244 297.893 75.8934 297.924 75.4644C297.942 75.2792 298.033 75.1084 298.175 74.9883C298.208 74.9688 298.245 74.9564 298.283 74.952C298.321 74.9476 298.36 74.9512 298.396 74.9627C298.44 74.9731 298.481 74.9904 298.519 75.0139C298.545 75.0333 298.565 75.0601 298.576 75.0907C298.737 75.5059 298.856 75.9365 298.93 76.3758C299.022 76.8264 299.099 77.2923 299.171 77.7582C299.243 78.2241 299.31 78.6952 299.366 79.1713C299.41 79.6627 299.489 80.1503 299.602 80.6305C299.663 80.8761 299.74 81.1172 299.833 81.3524C299.886 81.4841 299.959 81.6068 300.049 81.716C300.084 81.7613 300.131 81.796 300.184 81.8159C300.238 81.8359 300.296 81.8403 300.352 81.8286C300.404 81.8115 300.451 81.7813 300.487 81.7409C300.524 81.7006 300.55 81.6514 300.562 81.5982C300.623 81.3366 300.642 81.067 300.619 80.7995C300.619 80.5435 300.588 80.2875 300.562 80.0417C300.526 79.6116 300.485 79.1867 300.47 78.7668C300.457 78.5605 300.457 78.3536 300.47 78.1473C300.48 78.056 300.503 77.9664 300.537 77.8811C300.547 77.8529 300.563 77.8269 300.583 77.8043H300.65C300.988 77.666 301.101 78.1371 301.194 78.516C301.286 78.8948 301.322 79.3249 301.404 79.7294C301.538 80.5332 301.712 81.3371 301.799 82.1051C301.859 82.4728 301.845 82.8486 301.758 83.211C301.635 83.5796 301.512 83.9432 301.394 84.3067V84.3425V84.3732C301.995 86.8886 302.762 89.3618 303.689 91.7768C304.5 93.963 305.316 95.8728 306.029 97.4088C306.743 98.9448 307.39 100.148 307.831 100.957L308.344 101.873C308.4 101.983 308.467 102.086 308.545 102.181C308.502 102.067 308.448 101.957 308.386 101.853L307.872 100.916C307.451 100.092 306.846 98.868 306.147 97.332C305.449 95.796 304.659 93.8862 303.868 91.7C302.964 89.2886 302.213 86.8227 301.62 84.3169V84.3886C301.738 84.0302 301.856 83.6667 301.979 83.3032C302.086 82.9042 302.107 82.4871 302.041 82.0795C301.948 81.2756 301.779 80.4974 301.645 79.6884C301.568 79.2891 301.517 78.8795 301.435 78.4596C301.43 78.244 301.393 78.0301 301.327 77.8248C301.269 77.6998 301.169 77.5987 301.045 77.538C300.913 77.495 300.771 77.495 300.639 77.538H300.557C300.514 77.5519 300.474 77.5746 300.439 77.6046C300.382 77.6528 300.338 77.7144 300.311 77.7838C300.264 77.8946 300.234 78.0121 300.224 78.132C300.208 78.3536 300.208 78.5759 300.224 78.7975C300.224 79.2276 300.285 79.6577 300.321 80.0878C300.321 80.3387 300.362 80.5998 300.372 80.8302C300.393 81.0611 300.381 81.2938 300.336 81.5214C300.336 81.5726 300.295 81.5624 300.336 81.588L300.306 81.5572C300.24 81.4736 300.188 81.3803 300.152 81.2808C300.062 81.0593 299.988 80.8317 299.931 80.5998C299.818 80.1335 299.741 79.6594 299.7 79.1816C299.645 78.7003 299.578 78.2241 299.5 77.7531C299.428 77.282 299.346 76.8212 299.253 76.3604C299.176 75.8914 299.045 75.4328 298.863 74.9934C298.828 74.92 298.773 74.8579 298.704 74.8142C298.644 74.7796 298.58 74.7521 298.514 74.7323C298.441 74.7131 298.365 74.7087 298.29 74.7192C298.215 74.7298 298.143 74.7551 298.078 74.7937C297.972 74.8726 297.884 74.9724 297.818 75.087C297.753 75.2015 297.712 75.3283 297.698 75.4593C297.665 75.692 297.665 75.9281 297.698 76.1608C297.698 76.3809 297.749 76.596 297.765 76.811C297.836 77.6487 297.852 78.4903 297.811 79.33C297.799 79.5295 297.773 79.728 297.734 79.924C297.719 80.0155 297.693 80.1049 297.657 80.1902C297.644 80.2239 297.624 80.2551 297.6 80.2824C297.6 80.3284 297.564 80.2824 297.57 80.3438C297.57 80.2926 297.57 80.3438 297.57 80.2772C297.547 80.2427 297.526 80.2068 297.508 80.1697C297.472 80.0878 297.441 79.9956 297.41 79.9086C297.354 79.7243 297.313 79.5348 297.272 79.3505C297.004 77.9485 296.81 76.5334 296.692 75.1112C296.707 74.8537 296.678 74.5956 296.605 74.3483C296.571 74.2193 296.501 74.1022 296.404 74.0104C296.354 73.9619 296.295 73.9243 296.229 73.8996C296.164 73.875 296.094 73.8639 296.024 73.867H296.055C295.968 73.8543 295.88 73.8675 295.8 73.9048C295.721 73.9421 295.654 74.002 295.609 74.0769C295.532 74.199 295.48 74.3346 295.455 74.4763C295.412 74.7301 295.388 74.9868 295.383 75.2443C295.383 75.7563 295.383 76.2017 295.383 76.6369C295.432 77.3725 295.526 78.1043 295.665 78.8283C295.752 79.4222 295.819 79.883 295.85 80.1953C295.86 80.25 295.85 80.3064 295.823 80.3549C295.796 80.4035 295.753 80.4411 295.701 80.4615C295.547 80.4769 295.501 80.482 295.506 80.4872Z" fill="#A36957"/>
<path d="M262.073 106.614C261.072 106.592 260.072 106.708 259.101 106.957C256.447 107.653 251.874 110.418 251.684 113.833H255.01C255.01 113.833 253.147 117.786 253.691 117.417C255.113 116.465 257.936 113.603 257.936 113.603L262.073 106.614Z" fill="#B78876"/>
<path d="M307.415 47.1044C307.338 47.1044 307.04 44.3703 306.743 40.9604C306.445 37.5505 306.26 34.8164 306.332 34.8164C306.404 34.8164 306.707 37.5505 307.004 40.9604C307.302 44.3703 307.487 47.1044 307.415 47.1044Z" fill="#263238"/>
<path d="M321.999 35.0268C322.055 35.0677 319.812 38.3189 316.983 42.2869C314.155 46.2549 311.809 49.4549 311.747 49.3935C311.686 49.332 313.934 46.1013 316.763 42.1333C319.591 38.1653 321.937 34.9858 321.999 35.0268Z" fill="#263238"/>
<path d="M328.057 49.762C328.057 49.8336 325.45 50.4327 322.205 51.0983C318.961 51.7639 316.312 52.2452 316.297 52.1735C316.282 52.1018 318.9 51.5028 322.149 50.8372C325.398 50.1716 328.021 49.6903 328.057 49.762Z" fill="#263238"/>
<path d="M153.486 120.085L135.314 213.166L132.953 225.352L254.1 224.768C259.541 224.742 264.366 219.924 266.107 212.767L287.595 124.892C288.924 119.46 286.009 113.807 281.881 113.813L159.236 113.941C156.566 113.935 154.174 116.454 153.486 120.085Z" fill="#5F9EA0"/>
<g opacity="0.5">
<path d="M153.486 120.085L135.314 213.166L132.953 225.352L254.1 224.768C259.541 224.742 264.366 219.924 266.107 212.767L287.595 124.892C288.924 119.46 286.009 113.807 281.881 113.813L159.236 113.941C156.566 113.935 154.174 116.454 153.486 120.085Z" fill="black"/>
</g>
<path d="M242.494 206.848L229.661 103.726C229.147 99.5019 225.765 96.348 221.776 96.3633L193.645 96.4504C192.6 96.4621 191.568 96.6861 190.612 97.1086C189.656 97.5311 188.797 98.1434 188.086 98.908L174.739 113.075L96.5072 113.495C91.7281 113.52 88.0526 118.036 88.684 123.105L100.598 218.414C101.112 222.633 104.495 225.782 108.473 225.776L244.84 225.505C260.024 225.817 261.477 221.348 261.477 221.348C244.044 225.193 242.494 206.873 242.494 206.848Z" fill="#5F9EA0"/>
<g opacity="0.5">
<path d="M183.691 186.224L179.092 191.15L143.672 159.365L148.271 154.439L183.691 186.224Z" fill="white"/>
<path d="M177.166 153.349L182.248 157.911L150.2 192.24L145.113 187.678L177.166 153.349Z" fill="white"/>
</g>
<path d="M261.962 53.1349C261.796 53.0651 261.653 52.9495 261.55 52.8017C261.448 52.6539 261.389 52.48 261.382 52.3004L261.177 47.549C261.169 47.4014 261.197 47.2541 261.256 47.1188C261.316 46.9835 261.407 46.8639 261.521 46.7695C261.635 46.6752 261.769 46.6086 261.914 46.575C262.058 46.5414 262.208 46.5418 262.352 46.5762C262.75 46.6617 263.161 46.6654 263.559 46.5871C263.958 46.5087 264.337 46.35 264.672 46.1205C264.981 45.9256 265.247 45.6697 265.452 45.3685C265.658 45.0674 265.8 44.7274 265.869 44.3695C265.915 44.0071 265.888 43.6391 265.789 43.2873C265.691 42.9355 265.522 42.6071 265.294 42.3215C265.053 41.9965 264.751 41.7221 264.404 41.5139C264.056 41.3056 263.672 41.1677 263.271 41.108C262.463 41.0632 261.664 41.2916 261.003 41.7562C260.341 42.2208 259.856 42.8944 259.626 43.668C259.55 43.9024 259.385 44.0983 259.167 44.2148C258.95 44.3314 258.695 44.3596 258.457 44.2936C258.219 44.2276 258.015 44.0725 257.889 43.8606C257.762 43.6487 257.723 43.3963 257.778 43.156C258.128 41.9392 258.886 40.8795 259.927 40.155C260.967 39.4305 262.226 39.0854 263.492 39.1778C264.151 39.2653 264.785 39.484 265.357 39.8209C265.93 40.1578 266.428 40.6059 266.823 41.1388C267.208 41.6326 267.489 42.1983 267.65 42.8025C267.812 43.4067 267.85 44.037 267.763 44.6562C267.646 45.2745 267.404 45.8624 267.051 46.3836C266.697 46.9049 266.241 47.3486 265.709 47.6872C264.939 48.2044 264.04 48.497 263.112 48.532L263.271 52.198C263.277 52.359 263.242 52.519 263.17 52.663C263.097 52.807 262.989 52.9305 262.856 53.0219C262.723 53.1133 262.569 53.1697 262.408 53.1858C262.247 53.202 262.085 53.1775 261.936 53.1144L261.962 53.1349Z" fill="#5F9EA0"/>
<path d="M263.37 55.0808C263.38 55.2354 263.345 55.3897 263.268 55.5243C263.191 55.6588 263.075 55.7675 262.936 55.8365C262.797 55.9056 262.641 55.932 262.486 55.9123C262.332 55.8926 262.187 55.8277 262.07 55.7259C261.953 55.6241 261.869 55.4899 261.828 55.3403C261.787 55.1908 261.792 55.0325 261.842 54.8856C261.891 54.7387 261.983 54.6097 262.106 54.515C262.229 54.4204 262.378 54.3642 262.533 54.3537C262.636 54.3461 262.739 54.3589 262.837 54.3913C262.935 54.4238 263.026 54.4754 263.103 54.543C263.181 54.6106 263.245 54.693 263.291 54.7853C263.336 54.8776 263.363 54.978 263.37 55.0808Z" fill="#5F9EA0"/>
<path d="M245.47 46.433C245.492 46.3317 245.508 46.2291 245.517 46.1258C245.517 45.9005 245.573 45.6138 245.614 45.2093C245.665 44.7127 245.749 44.2201 245.866 43.7348C245.932 43.448 245.968 43.1306 246.071 42.808C246.174 42.4855 246.276 42.1373 246.389 41.784C246.983 40.0337 247.85 38.3872 248.956 36.9047C250.406 34.985 252.244 33.3907 254.351 32.225C255.55 31.5505 256.828 31.0244 258.155 30.6583C259.587 30.2796 261.062 30.0869 262.544 30.0848C263.324 30.0723 264.105 30.1134 264.879 30.2077C265.275 30.2487 265.665 30.3511 266.06 30.4176C266.46 30.4859 266.854 30.5817 267.241 30.7044C268.859 31.1553 270.398 31.8512 271.804 32.7677C274.932 34.7908 277.314 37.7741 278.591 41.2669C279.278 43.1379 279.626 45.1161 279.617 47.1088C279.638 49.1617 279.275 51.2003 278.544 53.1197L278.313 53.7034L278.283 53.7802L278.313 53.857C279.602 56.8112 280.88 59.7143 282.086 62.5252L282.302 62.2794L274.602 59.3354L274.51 59.2996L274.433 59.3712C272.396 61.3398 269.902 62.775 267.174 63.5492C264.843 64.1892 262.402 64.329 260.013 63.9592C257.624 63.5894 255.341 62.7185 253.314 61.4039C251.654 60.3181 250.191 58.959 248.987 57.3847C247.242 55.0867 246.111 52.3841 245.701 49.5306C245.64 49.0186 245.588 48.5885 245.542 48.1994C245.496 47.8103 245.542 47.4928 245.517 47.2215C245.491 46.9501 245.517 46.7709 245.517 46.6224C245.542 46.6186 245.565 46.6052 245.581 46.585C245.598 46.5648 245.605 46.5392 245.603 46.5135C245.602 46.4877 245.59 46.4637 245.571 46.4461C245.552 46.4286 245.527 46.4188 245.501 46.4188C245.475 46.4188 245.45 46.4286 245.431 46.4461C245.412 46.4637 245.401 46.4877 245.399 46.5135C245.397 46.5392 245.405 46.5648 245.421 46.585C245.437 46.6052 245.46 46.6186 245.486 46.6224C245.486 46.7709 245.486 46.9706 245.486 47.2215C245.486 47.4724 245.486 47.8205 245.486 48.2096C245.486 48.5988 245.573 49.0442 245.624 49.5511C245.793 50.7826 246.087 51.9937 246.502 53.1658C247.09 54.7136 247.88 56.177 248.853 57.5178C250.062 59.1202 251.536 60.5053 253.211 61.6138C255.263 62.9612 257.579 63.858 260.005 64.2446C262.431 64.6313 264.911 64.4989 267.282 63.8564C270.043 63.0743 272.568 61.6252 274.633 59.6375L274.463 59.6733L282.163 62.6327L282.548 62.7812L282.379 62.3869C281.157 59.576 279.894 56.6781 278.611 53.7188V53.8672L278.847 53.2682C279.596 51.307 279.969 49.2228 279.946 47.1242C279.962 45.0892 279.614 43.0676 278.919 41.1543C277.615 37.5919 275.18 34.5518 271.984 32.4964C270.542 31.5585 268.963 30.8503 267.302 30.3972C266.91 30.2732 266.508 30.179 266.101 30.1156C265.701 30.0439 265.305 29.9415 264.905 29.9056C264.12 29.8084 263.33 29.764 262.539 29.7725C261.033 29.7789 259.535 29.982 258.083 30.3767C256.738 30.7575 255.444 31.2973 254.228 31.9844C252.099 33.1786 250.25 34.8104 248.802 36.7716C247.699 38.2786 246.845 39.9517 246.271 41.7277C246.163 42.0964 246.061 42.4496 245.968 42.7517C245.887 43.0604 245.822 43.3731 245.773 43.6887C245.663 44.1773 245.591 44.6737 245.558 45.1735C245.527 45.5677 245.506 45.8698 245.491 46.0951C245.469 46.2063 245.462 46.32 245.47 46.433Z" fill="#263238"/>
</g>
</svg>`

export const pwdeye = `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.58 11.9999C15.58 13.9799 13.98 15.5799 12 15.5799C10.02 15.5799 8.42 13.9799 8.42 11.9999C8.42 10.0199 10.02 8.41992 12 8.41992C13.98 8.41992 15.58 10.0199 15.58 11.9999Z" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12 20.27C15.53 20.27 18.82 18.19 21.11 14.59C22.01 13.18 22.01 10.81 21.11 9.39997C18.82 5.79997 15.53 3.71997 12 3.71997C8.47 3.71997 5.18 5.79997 2.89 9.39997C1.99 10.81 1.99 13.18 2.89 14.59C5.18 18.19 8.47 20.27 12 20.27Z" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`

export const eyeclose = `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14.53 9.46992L9.47001 14.5299C8.82001 13.8799 8.42001 12.9899 8.42001 11.9999C8.42001 10.0199 10.02 8.41992 12 8.41992C12.99 8.41992 13.88 8.81992 14.53 9.46992Z" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17.82 5.76998C16.07 4.44998 14.07 3.72998 12 3.72998C8.47 3.72998 5.18 5.80998 2.89 9.40998C1.99 10.82 1.99 13.19 2.89 14.6C3.68 15.84 4.6 16.91 5.6 17.77" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8.42001 19.5299C9.56001 20.0099 10.77 20.2699 12 20.2699C15.53 20.2699 18.82 18.1899 21.11 14.5899C22.01 13.1799 22.01 10.8099 21.11 9.39993C20.78 8.87993 20.42 8.38993 20.05 7.92993" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M15.51 12.7C15.25 14.11 14.1 15.26 12.69 15.52" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.47 14.53L2 22" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M22 2L14.53 9.47" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`

export const colhotspot = `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.64256 17.763L8.64263 17.763L9.07371 17.3875L9.16607 17.4048C9.16607 17.4048 9.16608 17.4048 9.16608 17.4047C9.21179 17.1612 9.05141 16.9269 8.80792 16.8811L8.8079 16.8811C5.60786 16.2805 3.28498 13.4795 3.28498 10.221C3.28498 6.48439 6.3252 3.44433 10.062 3.44433C13.7987 3.44433 16.8391 6.48439 16.8391 10.221C16.8391 13.4795 14.5162 16.2805 11.3162 16.8811L11.3162 16.8811C11.0726 16.9269 10.9125 17.1613 10.958 17.4047L10.958 17.4048C10.9984 17.6205 11.1869 17.7706 11.3984 17.7706C11.4255 17.7706 11.4535 17.7682 11.4815 17.7628C15.106 17.0827 17.7363 13.9108 17.7363 10.221C17.7363 5.98947 14.2938 2.5472 10.0621 2.5472C5.83047 2.5472 2.38802 5.98947 2.38802 10.221C2.38802 13.9108 5.01828 17.0827 8.64256 17.763Z" fill="white" stroke="white" stroke-width="0.187944"/>
<path opacity="0.5" d="M11.2519 13.6802L11.252 13.6802C12.7279 13.1704 13.7196 11.7802 13.7196 10.2211C13.7196 8.20417 12.0786 6.56316 10.0616 6.56316C8.04464 6.56316 6.40363 8.20417 6.40363 10.2211C6.40363 11.7802 7.39529 13.1704 8.87124 13.68C9.10543 13.7608 9.22965 14.0163 9.14871 14.2504C9.14871 14.2504 9.1487 14.2504 9.1487 14.2504L9.05989 14.2197C9.00918 14.3665 8.87177 14.4587 8.72479 14.4587C8.68632 14.4587 8.64732 14.4525 8.60902 14.4392L11.2519 13.6802ZM11.2519 13.6802C11.0179 13.7612 10.8936 14.0163 10.9745 14.2505C11.0553 14.4848 11.3108 14.609 11.5449 14.528C13.3823 13.8935 14.6167 12.1626 14.6167 10.221C14.6167 7.70925 12.5733 5.66585 10.0616 5.66585C7.5499 5.66585 5.5065 7.70925 5.5065 10.221C5.5065 12.1628 6.74096 13.8935 8.57835 14.528L11.2519 13.6802Z" fill="white" stroke="white" stroke-width="0.187944"/>
<path d="M10.0613 8.8786C9.32127 8.8786 8.71918 9.4807 8.71918 10.2206C8.71918 10.9606 9.32127 11.5627 10.0613 11.5627C10.8013 11.5627 11.4034 10.9606 11.4034 10.2206C11.4034 9.48052 10.8013 8.8786 10.0613 8.8786ZM10.0613 10.8535C9.71239 10.8535 9.42836 10.5695 9.42836 10.2206C9.42836 9.87164 9.71239 9.58779 10.0613 9.58779C10.4102 9.58779 10.6943 9.87164 10.6943 10.2206C10.6943 10.5697 10.4104 10.8535 10.0613 10.8535Z" fill="white" stroke="white" stroke-width="1.25296"/>
</svg>
`
export const warning =`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.99563 12C8.20688 12 8.38542 11.9285 8.53125 11.7856C8.67708 11.6427 8.75 11.4656 8.75 11.2544C8.75 11.0431 8.67854 10.8646 8.53563 10.7188C8.39271 10.5729 8.21563 10.5 8.00438 10.5C7.79313 10.5 7.61458 10.5715 7.46875 10.7144C7.32292 10.8573 7.25 11.0344 7.25 11.2456C7.25 11.4569 7.32146 11.6354 7.46438 11.7812C7.60729 11.9271 7.78438 12 7.99563 12ZM7.25 9H8.75V4H7.25V9ZM8.00583 16C6.90472 16 5.86806 15.7917 4.89583 15.375C3.92361 14.9583 3.07292 14.3854 2.34375 13.6562C1.61458 12.9271 1.04167 12.0767 0.625 11.105C0.208333 10.1333 0 9.09514 0 7.99042C0 6.88569 0.208333 5.85069 0.625 4.88542C1.04167 3.92014 1.61458 3.07292 2.34375 2.34375C3.07292 1.61458 3.92333 1.04167 4.895 0.625C5.86667 0.208333 6.90486 0 8.00958 0C9.11431 0 10.1493 0.208333 11.1146 0.625C12.0799 1.04167 12.9271 1.61458 13.6562 2.34375C14.3854 3.07292 14.9583 3.92167 15.375 4.89C15.7917 5.85847 16 6.89319 16 7.99417C16 9.09528 15.7917 10.1319 15.375 11.1042C14.9583 12.0764 14.3854 12.9271 13.6562 13.6562C12.9271 14.3854 12.0783 14.9583 11.11 15.375C10.1415 15.7917 9.10681 16 8.00583 16Z" fill="#E6A957"/>
</svg>
`

export const elements = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_17030_2429" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
<rect width="24" height="24" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_17030_2429)">
<path d="M16.65 13.0002L11 7.3502L16.65 1.7002L22.3 7.3502L16.65 13.0002ZM3 11.0002V3.0002H11V11.0002H3ZM13 21.0002V13.0002H21V21.0002H13ZM3 21.0002V13.0002H11V21.0002H3ZM5 9.0002H9V5.0002H5V9.0002ZM16.675 10.2002L19.5 7.3752L16.675 4.5502L13.85 7.3752L16.675 10.2002ZM15 19.0002H19V15.0002H15V19.0002ZM5 19.0002H9V15.0002H5V19.0002Z" fill="#5F9EA0"/>
</g>
</svg>
`

export const overlay =`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_17030_2436" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
<rect width="24" height="24" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_17030_2436)">
<path d="M4 22C3.45 22 2.97917 21.8042 2.5875 21.4125C2.19583 21.0208 2 20.55 2 20V8C2 7.45 2.19583 6.97917 2.5875 6.5875C2.97917 6.19583 3.45 6 4 6H6V4C6 3.45 6.19583 2.97917 6.5875 2.5875C6.97917 2.19583 7.45 2 8 2H20C20.55 2 21.0208 2.19583 21.4125 2.5875C21.8042 2.97917 22 3.45 22 4V16C22 16.55 21.8042 17.0208 21.4125 17.4125C21.0208 17.8042 20.55 18 20 18H18V20C18 20.55 17.8042 21.0208 17.4125 21.4125C17.0208 21.8042 16.55 22 16 22H4ZM8 16H20V4H8V16Z" fill="#5F9EA0"/>
</g>
</svg>
`
export const animation =`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_17030_2443" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
<rect width="24" height="24" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_17030_2443)">
<path d="M9 22C8.03333 22 7.125 21.8167 6.275 21.45C5.425 21.0833 4.68333 20.5833 4.05 19.95C3.41667 19.3167 2.91667 18.575 2.55 17.725C2.18333 16.875 2 15.9667 2 15C2 13.65 2.35 12.4167 3.05 11.3C3.75 10.1833 4.66667 9.33333 5.8 8.75C6.13333 8.1 6.54583 7.52917 7.0375 7.0375C7.52917 6.54583 8.1 6.13333 8.75 5.8C9.3 4.66667 10.1417 3.75 11.275 3.05C12.4083 2.35 13.65 2 15 2C15.9667 2 16.875 2.18333 17.725 2.55C18.575 2.91667 19.3167 3.41667 19.95 4.05C20.5833 4.68333 21.0833 5.425 21.45 6.275C21.8167 7.125 22 8.03333 22 9C22 10.4167 21.65 11.6667 20.95 12.75C20.25 13.8333 19.3333 14.6667 18.2 15.25C17.8667 15.9 17.4542 16.4708 16.9625 16.9625C16.4708 17.4542 15.9 17.8667 15.25 18.2C14.6667 19.3333 13.8167 20.25 12.7 20.95C11.5833 21.65 10.35 22 9 22ZM9 20C9.55 20 10.0792 19.9167 10.5875 19.75C11.0958 19.5833 11.5667 19.3333 12 19C11.0333 19 10.125 18.8167 9.275 18.45C8.425 18.0833 7.68333 17.5833 7.05 16.95C6.41667 16.3167 5.91667 15.575 5.55 14.725C5.18333 13.875 5 12.9667 5 12C4.66667 12.4333 4.41667 12.9042 4.25 13.4125C4.08333 13.9208 4 14.45 4 15C4 15.7 4.13333 16.35 4.4 16.95C4.66667 17.55 5.025 18.075 5.475 18.525C5.925 18.975 6.45 19.3333 7.05 19.6C7.65 19.8667 8.3 20 9 20ZM12 17C12.55 17 13.0875 16.9167 13.6125 16.75C14.1375 16.5833 14.6167 16.3333 15.05 16C14.0667 16 13.15 15.8125 12.3 15.4375C11.45 15.0625 10.7083 14.5583 10.075 13.925C9.44167 13.2917 8.9375 12.55 8.5625 11.7C8.1875 10.85 8 9.93333 8 8.95C7.66667 9.38333 7.41667 9.8625 7.25 10.3875C7.08333 10.9125 7 11.45 7 12C7 12.7 7.12917 13.35 7.3875 13.95C7.64583 14.55 8.00833 15.075 8.475 15.525C8.925 15.9917 9.45 16.3542 10.05 16.6125C10.65 16.8708 11.3 17 12 17ZM15 14C15.3 14 15.5875 13.975 15.8625 13.925C16.1375 13.875 16.4167 13.8 16.7 13.7C17.0667 12.7 17.1208 11.7375 16.8625 10.8125C16.6042 9.8875 16.1583 9.10833 15.525 8.475C14.8917 7.84167 14.1125 7.39583 13.1875 7.1375C12.2625 6.87917 11.3 6.93333 10.3 7.3C10.2 7.58333 10.125 7.8625 10.075 8.1375C10.025 8.4125 10 8.7 10 9C10 9.7 10.1292 10.35 10.3875 10.95C10.6458 11.55 11.0083 12.075 11.475 12.525C11.925 12.9917 12.45 13.3542 13.05 13.6125C13.65 13.8708 14.3 14 15 14ZM19 12.05C19.3333 11.6167 19.5833 11.1375 19.75 10.6125C19.9167 10.0875 20 9.55 20 9C20 8.3 19.8708 7.65 19.6125 7.05C19.3542 6.45 18.9917 5.925 18.525 5.475C18.075 5.00833 17.55 4.64583 16.95 4.3875C16.35 4.12917 15.7 4 15 4C14.4167 4 13.8708 4.08333 13.3625 4.25C12.8542 4.41667 12.3833 4.66667 11.95 5C12.9333 5 13.85 5.1875 14.7 5.5625C15.55 5.9375 16.2917 6.44167 16.925 7.075C17.5583 7.70833 18.0625 8.45 18.4375 9.3C18.8125 10.15 19 11.0667 19 12.05Z" fill="#5F9EA0"/>
</g>
</svg>
`
export const TopLeft =`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="6.5" y="6" width="13" height="13" rx="2" fill="#5F9EA0"/>
<path d="M22.5 2H4.5C3.39543 2 2.5 2.89543 2.5 4V22" stroke="black" stroke-width="2" stroke-linecap="round"/>
</svg>
`
export const topcenter=`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="13" height="13" rx="2" transform="matrix(0 -1 -1 0 18.5 18)" fill="#5F9EA0"/>
<path d="M2.5 2H22.5" stroke="black" stroke-width="2" stroke-linecap="round"/>
</svg>
`
export const topright=`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="13" height="13" rx="2" transform="matrix(-1 0 0 1 18.5 6)" fill="#5F9EA0"/>
<path d="M2.5 2H20.5C21.6046 2 22.5 2.89543 22.5 4V22" stroke="black" stroke-width="2" stroke-linecap="round"/>
</svg>
`
export const centerleft=`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="13" height="13" rx="2" transform="matrix(-1 0 0 1 18.5 6)" fill="#5F9EA0"/>
<path d="M2.5 22V2" stroke="black" stroke-width="2" stroke-linecap="round"/>
</svg>
`
export const centercenter=`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="13" height="13" rx="2" transform="matrix(1 0 0 -1 6 18.5)" fill="#5F9EA0"/>
</svg>
`

export const centerright=`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="13" height="13" rx="2" transform="matrix(1 0 0 -1 6 18.5)" fill="#5F9EA0"/>
</svg>
`
export const bottomleft = `<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="13" height="13" rx="2" transform="matrix(1 0 0 -1 6.5 18)" fill="#5F9EA0"/>
<path d="M22.5 22H4.5C3.39543 22 2.5 21.1046 2.5 20V2" stroke="black" stroke-width="2" stroke-linecap="round"/>
</svg>
`

export const bottomcenter = `<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="18.5" y="6" width="13" height="13" rx="2" transform="rotate(90 18.5 6)" fill="#5F9EA0"/>
<path d="M2.5 22H22.5" stroke="black" stroke-width="2" stroke-linecap="round"/>
</svg>
`

export const bottomright=`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="18.5" y="18" width="13" height="13" rx="2" transform="rotate(-180 18.5 18)" fill="#5F9EA0"/>
<path d="M2.5 22H20.5C21.6046 22 22.5 21.1046 22.5 20V2" stroke="black" stroke-width="2" stroke-linecap="round"/>
</svg>
`
export const dotpos=`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_17035_5437" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
<rect x="0.5" width="24" height="24" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_17035_5437)">
<circle cx="12.668" cy="12" r="2" fill="#1C1B1F" fill-opacity="0.4"/>
</g>
</svg>
`



export const  ToursAnnouncementsIcon =`<svg width="103" height="86" viewBox="0 0 103 86" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.88479" y="1.10256" width="99.2308" height="83.7949" rx="3.30769" fill="#EFEFEF" stroke="#5F9EA0" stroke-width="2.20513"/>
<path d="M2.9873 4.41021C2.9873 3.19235 3.97457 2.20508 5.19243 2.20508H97.8078C99.0257 2.20508 100.013 3.19235 100.013 4.41021V44.1025H2.9873V4.41021Z" fill="#A7C7C8"/>
<rect x="53.7051" y="70.5635" width="35.2821" height="8.82051" fill="#5F9EA0"/>
<rect x="14.2332" y="70.784" width="34.841" height="8.37949" fill="#A7C7C8" stroke="#5F9EA0" stroke-width="0.441026"/>
<rect x="16.2178" y="48.5127" width="68.359" height="6.61538" rx="2.20513" fill="#D9D9D9"/>
<rect x="22.833" y="57.333" width="55.1282" height="2.20513" rx="1.10256" fill="#D9D9D9"/>
<rect x="22.833" y="61.7432" width="55.1282" height="2.20513" rx="1.10256" fill="#D9D9D9"/>
</svg>`

export const ToursHotspotIcon =`<svg width="89" height="103" viewBox="0 0 89 103" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M44.499 62.1578C48.0889 62.1578 50.999 59.2584 50.999 55.6819C50.999 52.1054 48.0889 49.2061 44.499 49.2061C40.9092 49.2061 37.999 52.1054 37.999 55.6819C37.999 59.2584 40.9092 62.1578 44.499 62.1578Z" fill="#5F9EA0"/>
<path d="M65.1699 79.5123C64.3173 79.5087 63.5003 79.1715 62.8949 78.5733C62.2896 77.9666 61.9498 77.146 61.9498 76.2906C61.9498 75.4352 62.2896 74.6145 62.8949 74.0078C66.5282 70.384 69.0016 65.7684 70.0024 60.7444C71.0033 55.7204 70.4867 50.5136 68.518 45.782C66.5493 41.0505 63.2168 37.0066 58.9417 34.1616C54.6667 31.3166 49.641 29.7981 44.5 29.7981C39.3589 29.7981 34.3332 31.3166 30.0582 34.1616C25.7831 37.0066 22.4506 41.0505 20.4819 45.782C18.5132 50.5136 17.9966 55.7204 18.9975 60.7444C19.9983 65.7684 22.4717 70.384 26.105 74.0078C26.4452 74.2981 26.7215 74.6553 26.9166 75.057C27.1117 75.4587 27.2213 75.8963 27.2386 76.3422C27.2559 76.7881 27.1804 77.2328 27.017 77.6483C26.8537 78.0638 26.6058 78.4412 26.2891 78.7567C25.9724 79.0723 25.5936 79.3192 25.1765 79.482C24.7595 79.6448 24.3131 79.7199 23.8656 79.7027C23.418 79.6855 22.9788 79.5763 22.5756 79.3819C22.1724 79.1875 21.8138 78.9123 21.5225 78.5733C17.7423 74.8083 14.9526 70.1725 13.4004 65.0767C11.8483 59.9809 11.5816 54.5823 12.6241 49.3592C13.6666 44.1362 15.986 39.2499 19.3769 35.1334C22.7677 31.0168 27.1254 27.797 32.0637 25.7593C37.002 23.7215 42.3686 22.9287 47.688 23.4511C53.0074 23.9735 58.1153 25.7949 62.5593 28.754C67.0032 31.7132 70.646 35.7186 73.1649 40.4155C75.6838 45.1125 77.001 50.3558 76.9999 55.6811C77.0115 59.9347 76.1758 64.1484 74.5412 68.0779C72.9066 72.0074 70.5056 75.5748 67.4774 78.5733C67.1737 78.8734 66.8136 79.1108 66.4176 79.272C66.0216 79.4331 65.5976 79.5148 65.1699 79.5123Z" fill="#5F9EA0"/>
<path d="M56.0051 70.3817C55.5751 70.3799 55.1497 70.2931 54.7536 70.1264C54.3576 69.9596 53.9986 69.7163 53.6976 69.4103C53.0923 68.8036 52.7525 67.983 52.7525 67.1276C52.7525 66.2722 53.0923 65.4515 53.6976 64.8448C55.5142 63.0329 56.7509 60.7251 57.2514 58.2131C57.7518 55.7011 57.4935 53.0977 56.5092 50.7319C55.5248 48.3662 53.8585 46.3442 51.721 44.9217C49.5835 43.4992 47.0707 42.74 44.5001 42.74C41.9296 42.74 39.4168 43.4992 37.2792 44.9217C35.1417 46.3442 33.4755 48.3662 32.4911 50.7319C31.5067 53.0977 31.2484 55.7011 31.7489 58.2131C32.2493 60.7251 33.486 63.0329 35.3026 64.8448C35.6429 65.1351 35.9192 65.4923 36.1142 65.894C36.3093 66.2957 36.4189 66.7333 36.4362 67.1792C36.4535 67.6251 36.3781 68.0698 36.2147 68.4853C36.0513 68.9008 35.8035 69.2782 35.4868 69.5937C35.17 69.9093 34.7912 70.1562 34.3742 70.319C33.9571 70.4818 33.5108 70.5569 33.0632 70.5397C32.6156 70.5225 32.1765 70.4132 31.7732 70.2189C31.37 70.0245 31.0115 69.7493 30.7201 69.4103C28.8987 67.6155 27.4547 65.477 26.4726 63.1197C25.4904 60.7625 24.9899 58.2338 25.0002 55.6815C25.0002 50.529 27.0546 45.5875 30.7116 41.9441C34.3685 38.3007 39.3284 36.2539 44.5001 36.2539C49.6718 36.2539 54.6317 38.3007 58.2887 41.9441C61.9456 45.5875 64.0001 50.529 64.0001 55.6815C64.0104 58.2338 63.5098 60.7625 62.5277 63.1197C61.5455 65.477 60.1015 67.6155 58.2801 69.4103C57.6801 70.0204 56.8624 70.3695 56.0051 70.3817Z" fill="#5F9EA0"/>
</svg>`

export const ToursBannerIcon = `<svg width="107" height="85" viewBox="0 0 107 85" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="0.695" y="1.21941" width="105.325" height="82.5612" rx="0.711667" fill="#EFEFEF" stroke="#5F9EA0" stroke-width="0.61"/>
<mask id="path-2-inside-1_16880_5264" fill="white">
<path d="M1 11.627H105.715V18.4562H1V11.627Z"/>
</mask>
<path d="M105.715 17.4396H1V19.4729H105.715V17.4396Z" fill="#E4E4E4" mask="url(#path-2-inside-1_16880_5264)"/>
<ellipse cx="4.4136" cy="4.93802" rx="1.13821" ry="1.13821" fill="#B5CFCF"/>
<ellipse cx="8.96829" cy="4.93802" rx="1.13821" ry="1.13821" fill="#B5CFCF"/>
<ellipse cx="13.52" cy="4.93802" rx="1.13821" ry="1.13821" fill="#B5CFCF"/>
<circle opacity="0.4" cx="5.06654" cy="15.6095" r="2.03333" fill="#5F9EA0"/>
<rect x="9.13281" y="15.0488" width="33.6288" height="1.12096" fill="#D9D9D9"/>
<rect opacity="0.4" x="94.999" y="13.5762" width="8.96768" height="4.06667" fill="#5F9EA0"/>
<rect x="96.2217" y="14.4814" width="6.1" height="1.01667" fill="#E4E4E4"/>
<rect x="23.1162" y="22.6689" width="78.8081" height="4.14779" fill="#D9D9D9"/>
<rect opacity="0.5" x="23.1162" y="26.8184" width="78.8081" height="3.4565" fill="#D9D9D9"/>
<rect opacity="0.5" x="23.1162" y="34.4209" width="78.8081" height="3.4565" fill="#D9D9D9"/>
<rect opacity="0.5" x="23.1162" y="41.333" width="78.8081" height="4.14779" fill="#D9D9D9"/>
<rect opacity="0.5" x="23.1162" y="48.9414" width="78.8081" height="4.14779" fill="#D9D9D9"/>
<rect opacity="0.5" x="23.1162" y="56.5449" width="78.8081" height="4.14779" fill="#D9D9D9"/>
<rect opacity="0.5" x="23.1162" y="63.3721" width="78.8081" height="4.14779" fill="#D9D9D9"/>
<rect opacity="0.5" x="23.1162" y="70.2012" width="78.8081" height="4.14779" fill="#D9D9D9"/>
<rect x="23.1162" y="30.2705" width="78.8081" height="4.14779" fill="#D9D9D9"/>
<rect x="23.1162" y="37.8779" width="78.8081" height="3.4565" fill="#D9D9D9"/>
<rect x="23.1162" y="45.4805" width="78.8081" height="3.4565" fill="#D9D9D9"/>
<rect x="23.1162" y="53.083" width="78.8081" height="3.4565" fill="#D9D9D9"/>
<rect x="23.1162" y="60.6865" width="78.8081" height="3.4565" fill="#D9D9D9"/>
<rect x="23.1162" y="67.5166" width="78.8081" height="3.4565" fill="#D9D9D9"/>
<rect x="23.1162" y="74.3447" width="78.8081" height="3.4565" fill="#D9D9D9"/>
<rect x="25.9668" y="24.0518" width="6.27487" height="1.25497" fill="#E4E4E4"/>
<rect x="33.4854" y="24.0518" width="6.27487" height="1.25497" fill="#E4E4E4"/>
<rect x="41.0889" y="24.0518" width="6.27487" height="1.25497" fill="#E4E4E4"/>
<rect x="48.6934" y="24.0518" width="6.27487" height="1.25497" fill="#E4E4E4"/>
<rect x="56.2959" y="24.0518" width="6.27487" height="1.25497" fill="#E4E4E4"/>
<rect x="63.9043" y="24.0518" width="6.27487" height="1.25497" fill="#E4E4E4"/>
<rect x="71.5068" y="24.0518" width="6.27487" height="1.25497" fill="#E4E4E4"/>
<rect x="79.1094" y="24.0518" width="6.27487" height="1.25497" fill="#E4E4E4"/>
<rect x="86.7129" y="24.0518" width="6.27487" height="1.25497" fill="#E4E4E4"/>
<rect x="94.3232" y="24.0518" width="6.27487" height="1.25497" fill="#E4E4E4"/>
<rect x="1" y="18.4561" width="18.2114" height="63.7398" fill="#E4E4E4"/>
<rect x="2.28027" y="19.4512" width="15.0597" height="3.76492" fill="#EFEFEF"/>
<rect opacity="0.5" x="2.28027" y="24.4678" width="15.0597" height="3.76492" fill="#F0F0F0"/>
<rect opacity="0.5" x="2.28027" y="29.4844" width="15.0597" height="3.76492" fill="#F0F0F0"/>
<rect opacity="0.5" x="2.28027" y="34.5117" width="15.0597" height="3.76492" fill="#F0F0F0"/>
<rect opacity="0.5" x="2.28027" y="39.5264" width="15.0597" height="3.76492" fill="#F0F0F0"/>
<rect opacity="0.4" x="3.5332" y="20.7041" width="6.27487" height="1.25497" fill="#5F9EA0"/>
<rect x="3.5332" y="25.7217" width="6.27487" height="1.25497" fill="#D9D9D9"/>
<rect x="3.5332" y="30.7451" width="6.27487" height="1.25497" fill="#D9D9D9"/>
<rect x="3.5332" y="35.7646" width="6.27487" height="1.25497" fill="#D9D9D9"/>
<rect x="3.5332" y="40.7842" width="6.27487" height="1.25497" fill="#D9D9D9"/>
<rect x="1" y="7.92676" width="105" height="5.12195" fill="#5F9EA0"/>
</svg>`

export const ToursTooltipIcon =`<svg width="72" height="62" viewBox="0 0 72 62" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M29.8852 31.4936C29.4371 30.7231 29.9549 29.7606 30.7941 29.8043L38.4941 30.2051C39.2951 30.2468 39.7926 31.1826 39.4009 31.9106L35.4657 39.2249C35.2306 39.6617 34.6329 39.6563 34.3763 39.2151L29.8852 31.4936Z" fill="#5F9EA0"/>
<rect y="0.68457" width="72" height="30.7522" rx="6.54545" fill="#5F9EA0"/>
<rect x="6.54492" y="10.1475" width="56.7273" height="3.3984" rx="1.6992" fill="#EAE2E2"/>
<rect x="6.54492" y="19.6094" width="16.5091" height="3.3984" rx="1.6992" fill="#EAE2E2"/>
<rect x="25.8047" y="19.6094" width="6.87879" height="3.3984" rx="1.6992" fill="#EAE2E2"/>
<rect x="11.2365" y="43.5919" width="48.1636" height="17.4364" rx="4.03636" fill="white"/>
<rect x="11.2365" y="43.5919" width="48.1636" height="17.4364" rx="4.03636" stroke="#5F9EA0" stroke-width="0.654545"/>
<path d="M23.3303 50.8627V52.1979H24.9012V52.8067H23.3303V54.2008H25.0976V54.8096H22.5841V50.2539H25.0976V50.8627H23.3303ZM26.7628 49.9659V54.8096H26.0167V49.9659H26.7628ZM31.048 52.9179C31.048 53.0532 31.0392 53.1754 31.0218 53.2845H28.2661C28.288 53.5725 28.3949 53.8038 28.5869 53.9783C28.7789 54.1528 29.0145 54.2401 29.2938 54.2401C29.6952 54.2401 29.9789 54.0721 30.1447 53.7361H30.9498C30.8407 54.0678 30.6421 54.3405 30.3541 54.5543C30.0705 54.7638 29.7171 54.8685 29.2938 54.8685C28.9491 54.8685 28.6392 54.7921 28.3643 54.6394C28.0938 54.4823 27.88 54.2641 27.7229 53.9848C27.5701 53.7012 27.4938 53.3739 27.4938 53.003C27.4938 52.6321 27.568 52.307 27.7163 52.0278C27.8691 51.7441 28.0807 51.5259 28.3512 51.3732C28.6261 51.2205 28.9403 51.1441 29.2938 51.1441C29.6341 51.1441 29.9374 51.2183 30.2036 51.3667C30.4698 51.515 30.6771 51.7245 30.8254 51.995C30.9738 52.2612 31.048 52.5688 31.048 52.9179ZM30.2691 52.6823C30.2647 52.4074 30.1665 52.187 29.9745 52.0212C29.7825 51.8554 29.5447 51.7725 29.2611 51.7725C29.0036 51.7725 28.7832 51.8554 28.6 52.0212C28.4167 52.1827 28.3076 52.403 28.2727 52.6823H30.2691ZM36.1481 51.1441C36.4317 51.1441 36.6848 51.203 36.9074 51.3208C37.1343 51.4387 37.311 51.6132 37.4376 51.8445C37.5685 52.0758 37.6339 52.355 37.6339 52.6823V54.8096H36.8943V52.7936C36.8943 52.4707 36.8136 52.2241 36.6521 52.0539C36.4906 51.8794 36.2703 51.7921 35.991 51.7921C35.7117 51.7921 35.4892 51.8794 35.3234 52.0539C35.1619 52.2241 35.0812 52.4707 35.0812 52.7936V54.8096H34.3416V52.7936C34.3416 52.4707 34.2608 52.2241 34.0994 52.0539C33.9379 51.8794 33.7176 51.7921 33.4383 51.7921C33.159 51.7921 32.9365 51.8794 32.7706 52.0539C32.6092 52.2241 32.5285 52.4707 32.5285 52.7936V54.8096H31.7823V51.203H32.5285V51.6154C32.6506 51.467 32.8056 51.3514 32.9932 51.2685C33.1808 51.1856 33.3816 51.1441 33.5954 51.1441C33.8834 51.1441 34.1408 51.2052 34.3677 51.3274C34.5946 51.4496 34.7692 51.6263 34.8914 51.8576C35.0005 51.6394 35.1706 51.467 35.4019 51.3405C35.6332 51.2096 35.8819 51.1441 36.1481 51.1441ZM41.8889 52.9179C41.8889 53.0532 41.8801 53.1754 41.8627 53.2845H39.1071C39.1289 53.5725 39.2358 53.8038 39.4278 53.9783C39.6198 54.1528 39.8554 54.2401 40.1347 54.2401C40.5361 54.2401 40.8198 54.0721 40.9856 53.7361H41.7907C41.6816 54.0678 41.4831 54.3405 41.1951 54.5543C40.9114 54.7638 40.558 54.8685 40.1347 54.8685C39.79 54.8685 39.4801 54.7921 39.2052 54.6394C38.9347 54.4823 38.7209 54.2641 38.5638 53.9848C38.4111 53.7012 38.3347 53.3739 38.3347 53.003C38.3347 52.6321 38.4089 52.307 38.5572 52.0278C38.71 51.7441 38.9216 51.5259 39.1921 51.3732C39.4671 51.2205 39.7812 51.1441 40.1347 51.1441C40.4751 51.1441 40.7783 51.2183 41.0445 51.3667C41.3107 51.515 41.518 51.7245 41.6663 51.995C41.8147 52.2612 41.8889 52.5688 41.8889 52.9179ZM41.11 52.6823C41.1056 52.4074 41.0074 52.187 40.8154 52.0212C40.6234 51.8554 40.3856 51.7725 40.102 51.7725C39.8445 51.7725 39.6241 51.8554 39.4409 52.0212C39.2576 52.1827 39.1485 52.403 39.1136 52.6823H41.11ZM44.4428 51.1441C44.7265 51.1441 44.9796 51.203 45.2021 51.3208C45.429 51.4387 45.6057 51.6132 45.7323 51.8445C45.8588 52.0758 45.9221 52.355 45.9221 52.6823V54.8096H45.1825V52.7936C45.1825 52.4707 45.1017 52.2241 44.9403 52.0539C44.7788 51.8794 44.5585 51.7921 44.2792 51.7921C43.9999 51.7921 43.7774 51.8794 43.6116 52.0539C43.4501 52.2241 43.3694 52.4707 43.3694 52.7936V54.8096H42.6232V51.203H43.3694V51.6154C43.4916 51.467 43.6465 51.3514 43.8341 51.2685C44.0261 51.1856 44.229 51.1441 44.4428 51.1441ZM47.7444 51.8118V53.8081C47.7444 53.9434 47.775 54.0416 47.8361 54.1027C47.9015 54.1594 48.0106 54.1878 48.1634 54.1878H48.6215V54.8096H48.0324C47.6964 54.8096 47.439 54.731 47.2601 54.5739C47.0812 54.4168 46.9917 54.1616 46.9917 53.8081V51.8118H46.5663V51.203H46.9917V50.3063H47.7444V51.203H48.6215V51.8118H47.7444Z" fill="#5F9EA0"/>
</svg>`

export const editline =`<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_17024_1572" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="14" height="15">
<rect y="0.813477" width="14" height="14" fill="#5F9D9F"/>
</mask>
<g mask="url(#mask0_17024_1572)">
<path d="M1.16602 14.8128V12.4795H12.8327V14.8128H1.16602ZM3.49935 10.1462H4.31602L8.86602 5.61074L8.03477 4.77949L3.49935 9.32949V10.1462ZM2.33268 11.3128V8.83366L8.86602 2.31491C8.97296 2.20796 9.09692 2.12533 9.23789 2.06699C9.37886 2.00866 9.52713 1.97949 9.68268 1.97949C9.83824 1.97949 9.98893 2.00866 10.1348 2.06699C10.2806 2.12533 10.4118 2.21283 10.5285 2.32949L11.3306 3.14616C11.4473 3.2531 11.5323 3.37949 11.5858 3.52533C11.6393 3.67116 11.666 3.82185 11.666 3.97741C11.666 4.12324 11.6393 4.26664 11.5858 4.40762C11.5323 4.54859 11.4473 4.67741 11.3306 4.79408L4.81185 11.3128H2.33268Z" fill="#7E7E7E"/>
</g>
</svg>
`
export const deletestep=`<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_17024_1512" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="18" height="18">
<rect width="18" height="18" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_17024_1512)">
<path d="M5.25 15.75C4.8375 15.75 4.48438 15.6031 4.19062 15.3094C3.89687 15.0156 3.75 14.6625 3.75 14.25V4.5C3.5375 4.5 3.35938 4.42813 3.21562 4.28438C3.07187 4.14063 3 3.9625 3 3.75C3 3.5375 3.07187 3.35938 3.21562 3.21563C3.35938 3.07188 3.5375 3 3.75 3H6.75C6.75 2.7875 6.82187 2.60938 6.96562 2.46562C7.10938 2.32187 7.2875 2.25 7.5 2.25H10.5C10.7125 2.25 10.8906 2.32187 11.0344 2.46562C11.1781 2.60938 11.25 2.7875 11.25 3H14.25C14.4625 3 14.6406 3.07188 14.7844 3.21563C14.9281 3.35938 15 3.5375 15 3.75C15 3.9625 14.9281 4.14063 14.7844 4.28438C14.6406 4.42813 14.4625 4.5 14.25 4.5V14.25C14.25 14.6625 14.1031 15.0156 13.8094 15.3094C13.5156 15.6031 13.1625 15.75 12.75 15.75H5.25ZM12.75 4.5H5.25V14.25H12.75V4.5ZM7.5 12.75C7.7125 12.75 7.89062 12.6781 8.03438 12.5344C8.17812 12.3906 8.25 12.2125 8.25 12V6.75C8.25 6.5375 8.17812 6.35938 8.03438 6.21562C7.89062 6.07187 7.7125 6 7.5 6C7.2875 6 7.10938 6.07187 6.96562 6.21562C6.82187 6.35938 6.75 6.5375 6.75 6.75V12C6.75 12.2125 6.82187 12.3906 6.96562 12.5344C7.10938 12.6781 7.2875 12.75 7.5 12.75ZM10.5 12.75C10.7125 12.75 10.8906 12.6781 11.0344 12.5344C11.1781 12.3906 11.25 12.2125 11.25 12V6.75C11.25 6.5375 11.1781 6.35938 11.0344 6.21562C10.8906 6.07187 10.7125 6 10.5 6C10.2875 6 10.1094 6.07187 9.96563 6.21562C9.82188 6.35938 9.75 6.5375 9.75 6.75V12C9.75 12.2125 9.82188 12.3906 9.96563 12.5344C10.1094 12.6781 10.2875 12.75 10.5 12.75Z" fill="#FF2F2F"/>
</g>
</svg>
`
export const defaultDots = `<svg width="5" height="4" viewBox="0 0 5 4" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="2.66797" cy="2" r="2" fill="#1C1B1F" fill-opacity="0.4"/>
</svg>
`
export const topLeft = `
<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="6.5" y="6" width="13" height="13" rx="2" fill="#5F9EA0"/>
<path d="M22.5 2H4.5C3.39543 2 2.5 2.89543 2.5 4V22" stroke="black" stroke-width="2" stroke-linecap="round"/>
</svg>
`
export const topCenter = `
<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="13" height="13" rx="2" transform="matrix(0 -1 -1 0 18.5 18)" fill="#5F9EA0"/>
<path d="M2.5 2H22.5" stroke="black" stroke-width="2" stroke-linecap="round"/>
</svg>
`
export const topRight = `
<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="13" height="13" rx="2" transform="matrix(-1 0 0 1 18.5 6)" fill="#5F9EA0"/>
<path d="M2.5 2H20.5C21.6046 2 22.5 2.89543 22.5 4V22" stroke="black" stroke-width="2" stroke-linecap="round"/>
</svg>
`
export const middleLeft = `
<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="13" height="13" rx="2" transform="matrix(-1 0 0 1 18.5 6)" fill="#5F9EA0"/>
<path d="M2.5 22V2" stroke="black" stroke-width="2" stroke-linecap="round"/>
</svg>
`
export const middleCenter = `
<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="13" height="13" rx="2" transform="matrix(1 0 0 -1 6 18.5)" fill="#5F9EA0"/>
</svg>
`
export const middleRight = `
<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="6.5" y="6" width="13" height="13" rx="2" fill="#5F9EA0"/>
<path d="M22.5 22V2" stroke="black" stroke-width="2" stroke-linecap="round"/>
</svg>
`
export const bottomLeft = `
<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="13" height="13" rx="2" transform="matrix(1 0 0 -1 6.5 18)" fill="#5F9EA0"/>
<path d="M22.5 22H4.5C3.39543 22 2.5 21.1046 2.5 20V2" stroke="black" stroke-width="2" stroke-linecap="round"/>
</svg>
`
export const bottomMiddle = `
<svg width="23" height="21" viewBox="0 0 23 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="17.5" y="4" width="13" height="13" rx="2" transform="rotate(90 17.5 4)" fill="#5F9EA0"/>
<path d="M1.5 20H21.5" stroke="black" stroke-width="2" stroke-linecap="round"/>
</svg>
`
export const bottomRight = `
<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="18.5" y="18" width="13" height="13" rx="2" transform="rotate(-180 18.5 18)" fill="#5F9EA0"/>
<path d="M2.5 22H20.5C21.6046 22 22.5 21.1046 22.5 20V2" stroke="black" stroke-width="2" stroke-linecap="round"/>
</svg>
`
export const tooltipTop = `
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.3333 25.3337H10.6666C5.33329 25.3337 2.66663 24.0003 2.66663 17.3337V10.667C2.66663 5.33366 5.33329 2.66699 10.6666 2.66699H21.3333C26.6666 2.66699 29.3333 5.33366 29.3333 10.667V17.3337C29.3333 22.667 26.6666 25.3337 21.3333 25.3337H20.6666C20.2533 25.3337 19.8533 25.5337 19.6 25.867L17.6 28.5337C16.72 29.707 15.28 29.707 14.4 28.5337L12.4 25.867C12.1866 25.5737 11.6933 25.3337 11.3333 25.3337Z" stroke="#5F9EA0" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.33337 10.667H22.6667" stroke="#5F9EA0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.33337 17.333H17.3334" stroke="#5F9EA0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`
export const tooltipRight =`
<svg width="30" height="26" viewBox="0 0 30 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.75 9.5H23.0833" stroke="#5F9EA0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.75 16.166H17.75" stroke="#5F9EA0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M5.21611 8.975V8.4C5.21611 3.8 6.51223 1.5 12.9928 1.5H20.9733C26.1578 1.5 28.75 3.8 28.75 8.4V17.6C28.75 22.2 26.1578 24.5 20.9733 24.5H12.9928C7.80834 24.5 5.21611 22.2 5.21611 17.6V17.025C5.21611 16.6685 5.0217 16.3235 4.69767 16.105L2.10545 14.38C0.964868 13.621 0.964868 12.379 2.10545 11.62L4.69767 9.895C4.98281 9.711 5.21611 9.2855 5.21611 8.975Z" stroke="#5F9EA0" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`
export const tooltipBottom = `
<svg width="32" height="33" viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.3333 7.16634H10.6666C5.33329 7.16634 2.66663 8.49967 2.66663 15.1663V21.833C2.66663 27.1663 5.33329 29.833 10.6666 29.833H21.3333C26.6666 29.833 29.3333 27.1663 29.3333 21.833V15.1663C29.3333 9.83301 26.6666 7.16634 21.3333 7.16634H20.6666C20.2533 7.16634 19.8533 6.96634 19.6 6.63301L17.6 3.96634C16.72 2.79301 15.28 2.79301 14.4 3.96634L12.4 6.63301C12.1866 6.92634 11.6933 7.16634 11.3333 7.16634Z" stroke="#5F9EA0" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.33337 21.833H22.6667" stroke="#5F9EA0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.33337 15.167H17.3334" stroke="#5F9EA0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`
export const tooltipLeft = `
<svg width="31" height="25" viewBox="0 0 31 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.24988 9H20.5832" stroke="#5F9EA0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.24988 15.666H15.2499" stroke="#5F9EA0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M25.1171 8.475V7.9C25.1171 3.3 23.821 1 17.3405 1H9.35992C4.17548 1 1.58325 3.3 1.58325 7.9V17.1C1.58325 21.7 4.17548 24 9.35992 24H17.3405C22.5249 24 25.1171 21.7 25.1171 17.1V16.525C25.1171 16.1685 25.3116 15.8235 25.6356 15.605L28.2278 13.88C29.3684 13.121 29.3684 11.879 28.2278 11.12L25.6356 9.395C25.3504 9.211 25.1171 8.7855 25.1171 8.475Z" stroke="#5F9EA0" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`
export const tooltipCenter = `
<svg width="60" height="26" viewBox="0 0 60 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="0.5" width="59" height="26" rx="2" fill="#94C0C1"/>
</svg>
`
export const editpricol =`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_18657_30467" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
<rect width="16" height="16" fill="#D8D8D8"/>
</mask>
<g mask="url(#mask0_18657_30467)">
<path d="M1.33398 16V13.3333H14.6673V16H1.33398ZM4.00065 10.6666H4.93398L10.134 5.48331L9.18398 4.53331L4.00065 9.73331V10.6666ZM2.66732 12V9.16665L10.134 1.71665C10.2562 1.59442 10.3979 1.49998 10.559 1.43331C10.7201 1.36665 10.8895 1.33331 11.0673 1.33331C11.2451 1.33331 11.4173 1.36665 11.584 1.43331C11.7507 1.49998 11.9007 1.59998 12.034 1.73331L12.9507 2.66665C13.084 2.78887 13.1812 2.93331 13.2423 3.09998C13.3034 3.26665 13.334 3.43887 13.334 3.61665C13.334 3.78331 13.3034 3.9472 13.2423 4.10831C13.1812 4.26942 13.084 4.41665 12.9507 4.54998L5.50065 12H2.66732Z" fill="#5F9EA0"/>
</g>
</svg>
`
export const chkicn1 = `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14.6826 8.0166H10.3076C9.96595 8.0166 9.68262 7.73327 9.68262 7.3916C9.68262 7.04993 9.96595 6.7666 10.3076 6.7666H14.6826C15.0243 6.7666 15.3076 7.04993 15.3076 7.3916C15.3076 7.73327 15.0326 8.0166 14.6826 8.0166Z" fill="#333333"/>
<path d="M5.93346 8.64965C5.77513 8.64965 5.6168 8.59132 5.4918 8.46632L4.8668 7.84132C4.62513 7.59965 4.62513 7.19965 4.8668 6.95798C5.10846 6.71632 5.50846 6.71632 5.75013 6.95798L5.93346 7.14132L7.3668 5.70798C7.60846 5.46632 8.00846 5.46632 8.25013 5.70798C8.4918 5.94965 8.4918 6.34965 8.25013 6.59132L6.37513 8.46632C6.25846 8.58298 6.10013 8.64965 5.93346 8.64965Z" fill="#333333"/>
<path d="M14.6826 13.8501H10.3076C9.96595 13.8501 9.68262 13.5668 9.68262 13.2251C9.68262 12.8834 9.96595 12.6001 10.3076 12.6001H14.6826C15.0243 12.6001 15.3076 12.8834 15.3076 13.2251C15.3076 13.5668 15.0326 13.8501 14.6826 13.8501Z" fill="#333333"/>
<path d="M5.93346 14.4834C5.77513 14.4834 5.6168 14.4251 5.4918 14.3001L4.8668 13.6751C4.62513 13.4334 4.62513 13.0334 4.8668 12.7917C5.10846 12.5501 5.50846 12.5501 5.75013 12.7917L5.93346 12.9751L7.3668 11.5417C7.60846 11.3001 8.00846 11.3001 8.25013 11.5417C8.4918 11.7834 8.4918 12.1834 8.25013 12.4251L6.37513 14.3001C6.25846 14.4167 6.10013 14.4834 5.93346 14.4834Z" fill="#333333"/>
<path d="M12.5003 18.9582H7.50033C2.97533 18.9582 1.04199 17.0248 1.04199 12.4998V7.49984C1.04199 2.97484 2.97533 1.0415 7.50033 1.0415H12.5003C17.0253 1.0415 18.9587 2.97484 18.9587 7.49984V12.4998C18.9587 17.0248 17.0253 18.9582 12.5003 18.9582ZM7.50033 2.2915C3.65866 2.2915 2.29199 3.65817 2.29199 7.49984V12.4998C2.29199 16.3415 3.65866 17.7082 7.50033 17.7082H12.5003C16.342 17.7082 17.7087 16.3415 17.7087 12.4998V7.49984C17.7087 3.65817 16.342 2.2915 12.5003 2.2915H7.50033Z" fill="#333333"/>
</svg>

`

export const chkicn2 = `<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15 9.14972V10.4248C15 12.7873 13.65 13.7998 11.625 13.7998H4.875C2.85 13.7998 1.5 12.7873 1.5 10.4248V6.37476C1.5 4.01226 2.85 2.99976 4.875 2.99976H6.9C6.8025 3.28476 6.75 3.59976 6.75 3.93726V6.86227C6.75 7.58977 6.99 8.20476 7.4175 8.63226C7.845 9.05976 8.46 9.29977 9.1875 9.29977V10.3423C9.1875 10.7248 9.6225 10.9572 9.945 10.7472L12.1125 9.29977H14.0625C14.4 9.29977 14.715 9.24722 15 9.14972Z" stroke="#333333" stroke-width="1.125" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16.5 3.93738V6.8624C16.5 7.9799 15.93 8.81984 15 9.14984C14.715 9.24734 14.4 9.2999 14.0625 9.2999H12.1125L9.945 10.7473C9.6225 10.9573 9.1875 10.7249 9.1875 10.3424V9.2999C8.46 9.2999 7.845 9.05988 7.4175 8.63238C6.99 8.20488 6.75 7.5899 6.75 6.8624V3.93738C6.75 3.59988 6.8025 3.28488 6.9 2.99988C7.23 2.06988 8.07 1.49988 9.1875 1.49988H14.0625C15.525 1.49988 16.5 2.47488 16.5 3.93738Z" stroke="#333333" stroke-width="1.125" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M5.55078 16.4998H10.9508" stroke="#333333" stroke-width="1.125" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8.25 13.8002V16.5002" stroke="#333333" stroke-width="1.125" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M13.8716 5.4375H13.8784" stroke="#333333" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M11.772 5.4375H11.7788" stroke="#333333" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.67338 5.4375H9.68013" stroke="#333333" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`

export const chkicn3 = `<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M6 1.50024V3.75024" stroke="#333333" stroke-width="1.125" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12 1.50024V3.75024" stroke="#333333" stroke-width="1.125" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M15.75 6.37524V10.2227C15.0825 9.69025 14.235 9.37524 13.3125 9.37524C12.39 9.37524 11.5275 9.69772 10.8525 10.2452C9.945 10.9577 9.375 12.0752 9.375 13.3127C9.375 14.0477 9.58501 14.7528 9.94501 15.3378C10.2225 15.7953 10.5825 16.1927 11.01 16.5002H6C3.375 16.5002 2.25 15.0002 2.25 12.7502V6.37524C2.25 4.12524 3.375 2.62524 6 2.62524H12C14.625 2.62524 15.75 4.12524 15.75 6.37524Z" stroke="#333333" stroke-width="1.125" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M5.25 8.25024H9.75" stroke="#333333" stroke-width="1.125" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M5.25 12.0001H7.215" stroke="#333333" stroke-width="1.125" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17.25 13.3127C17.25 14.0477 17.04 14.7528 16.68 15.3378C16.47 15.6978 16.2075 16.0127 15.9 16.2677C15.21 16.8827 14.31 17.2502 13.3125 17.2502C12.45 17.2502 11.655 16.9727 11.01 16.5002C10.5825 16.1927 10.2225 15.7953 9.94501 15.3378C9.58501 14.7528 9.375 14.0477 9.375 13.3127C9.375 12.0752 9.945 10.9577 10.8525 10.2452C11.5275 9.69772 12.39 9.37524 13.3125 9.37524C14.235 9.37524 15.0825 9.69025 15.75 10.2227C16.665 10.9427 17.25 12.0602 17.25 13.3127Z" stroke="#333333" stroke-width="1.125" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M13.3125 15.1876C13.3125 14.1526 14.1525 13.3126 15.1875 13.3126C14.1525 13.3126 13.3125 12.4726 13.3125 11.4376C13.3125 12.4726 12.4725 13.3126 11.4375 13.3126C12.4725 13.3126 13.3125 14.1526 13.3125 15.1876Z" stroke="#333333" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`

export const chkicn4 =` <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M6.98242 11.0258L8.10742 12.1508L11.1074 9.15076" stroke="#333333" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.5 4.50024H10.5C12 4.50024 12 3.75024 12 3.00024C12 1.50024 11.25 1.50024 10.5 1.50024H7.5C6.75 1.50024 6 1.50024 6 3.00024C6 4.50024 6.75 4.50024 7.5 4.50024Z" stroke="#333333" stroke-width="1.125" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12 3.01416C14.4975 3.14916 15.75 4.07166 15.75 7.49916V11.9992C15.75 14.9992 15 16.4992 11.25 16.4992H6.75C3 16.4992 2.25 14.9992 2.25 11.9992V7.49916C2.25 4.07916 3.5025 3.14916 6 3.01416" stroke="#333333" stroke-width="1.125" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`

export const chkicn5 = `<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.86762 1.6734L4.12512 3.0834C3.26262 3.4059 2.55762 4.4259 2.55762 5.3409V10.9134C2.55762 11.7984 3.14262 12.9609 3.85512 13.4934L7.08012 15.9009C8.13762 16.6959 9.87762 16.6959 10.9351 15.9009L14.1601 13.4934C14.8726 12.9609 15.4576 11.7984 15.4576 10.9134V5.3409C15.4576 4.4184 14.7526 3.3984 13.8901 3.0759L10.1476 1.6734C9.51012 1.4409 8.49012 1.4409 7.86762 1.6734Z" stroke="#333333" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6.78613 8.90288L7.99363 10.1104L11.2186 6.88538" stroke="#333333" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`

export const chkicn6 = `<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame 1948755312">
<path id="Vector" d="M13.875 14.625H10.875" stroke="#333333" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Vector_2" d="M12.375 16.1248V13.1248" stroke="#333333" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Vector_3" d="M9.11995 8.15348C9.04495 8.14598 8.95495 8.14598 8.87245 8.15348C7.08745 8.09348 5.66995 6.63098 5.66995 4.83098C5.66245 2.99348 7.15495 1.50098 8.99245 1.50098C10.83 1.50098 12.3225 2.99348 12.3225 4.83098C12.3225 6.63098 10.8974 8.09348 9.11995 8.15348Z" stroke="#333333" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Vector_4" d="M8.99277 16.3575C7.62777 16.3575 6.27027 16.0125 5.23527 15.3225C3.42027 14.1075 3.42027 12.1275 5.23527 10.92C7.29777 9.54001 10.6803 9.54001 12.7428 10.92" stroke="#333333" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</svg>
`
export const redirect =`<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_18657_36953" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
<rect width="24" height="24" fill="#444444"/>
</mask>
<g mask="url(#mask0_18657_36953)">
<path d="M5 21C4.45 21 3.97917 20.8042 3.5875 20.4125C3.19583 20.0208 3 19.55 3 19V5C3 4.45 3.19583 3.97917 3.5875 3.5875C3.97917 3.19583 4.45 3 5 3H12V5H5V19H19V12H21V19C21 19.55 20.8042 20.0208 20.4125 20.4125C20.0208 20.8042 19.55 21 19 21H5ZM9.7 15.7L8.3 14.3L17.6 5H14V3H21V10H19V6.4L9.7 15.7Z" fill="#444444"/>
</g>
</svg>
`


export const check =`  <svg width="12" height="12" viewBox="0 0 11 8" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1 4.2L3.8 7L9.4 1" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`

export const maximize =`<svg width="18" height="18" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1.61523 5.38471V4.03856C1.61523 2.69779 2.69754 1.61548 4.03831 1.61548H5.38446" stroke="#333333" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8.61523 1.61548H9.96139C11.3022 1.61548 12.3845 2.69779 12.3845 4.03856V5.38471" stroke="#333333" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12.3851 9.15405V9.96175C12.3851 11.3025 11.3028 12.3848 9.96199 12.3848H9.1543" stroke="#333333" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M5.38446 12.3847H4.03831C2.69754 12.3847 1.61523 11.3024 1.61523 9.96163V8.61548" stroke="#333333" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`

export const chkdefault =`<svg width="249" height="224" viewBox="0 0 249 248" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M23.5316 235.225C22.8774 234.664 21.4177 232.886 22.5431 230.819C22.9558 230.06 23.8322 229.685 24.6258 229.351C25.4189 229.017 26.2527 228.727 26.9094 228.168C28.1474 227.115 28.4891 225.365 29.3105 223.96C30.2539 222.347 32.0147 221.139 33.8752 221.223C36.0055 221.32 37.7827 223.06 38.5877 225.044C39.3932 227.028 39.4215 229.231 39.4378 231.374C39.4473 232.558 39.3947 233.894 38.5247 234.694C37.7227 235.431 36.5154 235.44 35.4287 235.407C31.548 235.288 27.6668 235.168 23.7861 235.049" fill="#F5F5F5"/>
<path d="M45.7992 203.209H15.1807L20.6962 236.097C20.9372 237.534 21.9764 238.641 23.3101 239.025C23.3453 239.036 23.3815 239.044 23.4172 239.054C23.5085 239.078 23.5963 239.108 23.69 239.125C23.8919 239.166 24.1007 239.188 24.3125 239.196C24.314 239.207 24.316 239.218 24.3175 239.228L24.4668 239.204H24.4678H38.1137C38.3706 239.204 38.6211 239.178 38.8637 239.129C40.4752 238.88 41.7653 237.587 41.9612 235.915L44.6202 213.255L44.7403 212.354H44.7259L45.7789 203.38L45.7992 203.209ZM40.0233 224.188H36.8812L37.1981 214.643H40.8387L40.0233 224.188ZM41.1438 214.643H44.152L43.0321 224.188H40.3284L41.1438 214.643ZM32.1667 234.768L31.9757 226.476H36.5007L36.2254 234.768H32.1667ZM27.2751 226.477H31.6717L31.8626 234.769H27.9194L27.2751 226.477ZM27.6149 234.768H23.9286C23.5784 232.53 23.1226 229.62 22.6291 226.476H26.9706L27.6149 234.768ZM27.0971 224.188L26.3555 214.643H31.3994L31.6191 224.188H27.0971ZM31.9231 224.188L31.7034 214.643H36.8941L36.5771 224.188H31.9231ZM31.6503 212.354L31.447 203.515H37.2636L36.9699 212.354H31.6503ZM31.3463 212.354H26.1775L25.4905 203.515H31.1429L31.3463 212.354ZM25.8724 212.354H20.4035C20.2101 211.134 20.0261 209.978 19.8604 208.938C19.29 205.365 19.0753 204.021 18.9562 203.515H25.1855L25.8724 212.354ZM26.0505 214.643L26.792 224.188H22.269C22.1167 223.219 21.963 222.24 21.8087 221.259C21.4541 219.006 21.0989 216.754 20.7656 214.643H26.0505ZM21.962 224.188H19.0073L17.4062 214.643H20.4581C20.9243 217.593 21.4417 220.876 21.962 224.188ZM22.3211 226.477C22.7714 229.346 23.2149 232.175 23.6206 234.769H20.782L19.3912 226.477H22.3211ZM36.5295 234.768L36.8048 226.476H39.8274L39.1191 234.768H36.5295ZM37.274 212.354L37.5676 203.515H41.7896L41.0347 212.354H37.274ZM18.667 203.515C18.7886 204.154 19.3471 207.632 20.096 212.354H17.0223L15.5398 203.515H18.667ZM41.7906 234.768H39.4242L40.1324 226.476H42.7632L41.7906 234.768ZM44.4204 212.354H41.3392L42.0946 203.515H45.458L44.4204 212.354Z" fill="#E0E0E0"/>
<path d="M231.937 63.2151H190.669V100.136H231.937V63.2151Z" fill="#FAFAFA"/>
<path d="M231.938 100.135C231.938 100.135 231.931 99.8988 231.927 99.4499C231.925 98.997 231.922 98.3344 231.917 97.4718C231.911 95.7383 231.903 93.2047 231.892 89.9773C231.881 83.5263 231.864 74.3086 231.844 63.2146L231.938 63.3079C220.053 63.3128 205.87 63.3183 190.674 63.3247H190.67L190.78 63.2146C190.775 76.6468 190.771 89.2863 190.767 100.135L190.67 100.038C202.981 100.058 213.285 100.075 220.517 100.087C224.136 100.098 226.985 100.107 228.936 100.113C229.908 100.118 230.655 100.122 231.165 100.124C231.414 100.127 231.605 100.129 231.739 100.13C231.869 100.131 231.938 100.135 231.938 100.135C231.938 100.135 231.877 100.139 231.751 100.141C231.619 100.142 231.432 100.144 231.187 100.146C230.681 100.149 229.94 100.153 228.977 100.158C227.031 100.164 224.19 100.173 220.58 100.183C213.332 100.195 203.007 100.212 190.67 100.233H190.573V100.135C190.569 89.2868 190.565 76.6468 190.56 63.2146V63.1045H190.67H190.673C205.869 63.1104 220.052 63.1164 231.937 63.1214H232.031V63.2151C232.011 74.3334 231.994 83.5719 231.983 90.0368C231.973 93.2548 231.964 95.782 231.959 97.51C231.954 98.3651 231.95 99.0213 231.948 99.4702C231.945 99.9122 231.938 100.135 231.938 100.135Z" fill="#E0E0E0"/>
<path d="M206.493 69.7752C206.493 69.8362 204.321 69.8853 201.642 69.8853C198.963 69.8853 196.791 69.8357 196.791 69.7752C196.791 69.7141 198.963 69.665 201.642 69.665C204.321 69.6645 206.493 69.7141 206.493 69.7752Z" fill="#E0E0E0"/>
<path d="M223.979 72.5471C223.979 72.6081 218.011 72.6572 210.652 72.6572C203.29 72.6572 197.323 72.6081 197.323 72.5471C197.323 72.4861 203.29 72.437 210.652 72.437C218.011 72.4365 223.979 72.4861 223.979 72.5471Z" fill="#E0E0E0"/>
<path d="M198.6 77.2429H196.861V78.9814H198.6V77.2429Z" fill="#E0E0E0"/>
<path d="M202.88 77.2429H201.142V78.9814H202.88V77.2429Z" fill="#E0E0E0"/>
<path d="M207.238 77.1643H205.5V78.9028H207.238V77.1643Z" fill="#E0E0E0"/>
<path d="M211.439 77.2429H209.7V78.9814H211.439V77.2429Z" fill="#E0E0E0"/>
<path d="M215.719 77.2429H213.98V78.9814H215.719V77.2429Z" fill="#E0E0E0"/>
<path d="M219.998 77.2429H218.26V78.9814H219.998V77.2429Z" fill="#E0E0E0"/>
<path d="M224.278 77.2429H222.539V78.9814H224.278V77.2429Z" fill="#EBEBEB"/>
<path d="M198.6 80.939H196.861V82.6774H198.6V80.939Z" fill="#EBEBEB"/>
<path d="M202.88 80.939H201.142V82.6774H202.88V80.939Z" fill="#E0E0E0"/>
<path d="M207.159 80.939H205.421V82.6774H207.159V80.939Z" fill="#EBEBEB"/>
<path d="M211.439 80.939H209.7V82.6774H211.439V80.939Z" fill="#E0E0E0"/>
<path d="M215.719 80.939H213.98V82.6774H215.719V80.939Z" fill="#E0E0E0"/>
<path d="M220.077 80.8604H218.339V82.5988H220.077V80.8604Z" fill="#E0E0E0"/>
<path d="M224.278 80.939H222.539V82.6774H224.278V80.939Z" fill="#E0E0E0"/>
<path d="M198.6 84.6355H196.861V86.374H198.6V84.6355Z" fill="#E0E0E0"/>
<path d="M202.959 84.5564H201.221V86.2949H202.959V84.5564Z" fill="#E0E0E0"/>
<path d="M207.159 84.6355H205.421V86.374H207.159V84.6355Z" fill="#E0E0E0"/>
<path d="M211.439 84.6355H209.7V86.374H211.439V84.6355Z" fill="#EBEBEB"/>
<path d="M215.719 84.6355H213.98V86.374H215.719V84.6355Z" fill="#E0E0E0"/>
<path d="M219.998 84.6355H218.26V86.374H219.998V84.6355Z" fill="#E0E0E0"/>
<path d="M224.278 84.6355H222.539V86.374H224.278V84.6355Z" fill="#E0E0E0"/>
<path d="M198.6 88.3315H196.861V90.07H198.6V88.3315Z" fill="#E0E0E0"/>
<path d="M202.88 88.3315H201.142V90.07H202.88V88.3315Z" fill="#E0E0E0"/>
<path d="M207.159 88.3315H205.421V90.07H207.159V88.3315Z" fill="#E0E0E0"/>
<path d="M211.518 88.2524H209.779V89.9909H211.518V88.2524Z" fill="#E0E0E0"/>
<path d="M215.719 88.3315H213.98V90.07H215.719V88.3315Z" fill="#E0E0E0"/>
<path d="M219.998 88.3315H218.26V90.07H219.998V88.3315Z" fill="#EBEBEB"/>
<path d="M224.278 88.3315H222.539V90.07H224.278V88.3315Z" fill="#E0E0E0"/>
<path d="M198.6 92.0276H196.861V93.7661H198.6V92.0276Z" fill="#E0E0E0"/>
<path d="M202.88 92.0276H201.142V93.7661H202.88V92.0276Z" fill="#EBEBEB"/>
<path d="M207.159 92.0276H205.421V93.7661H207.159V92.0276Z" fill="#E0E0E0"/>
<path d="M211.439 92.0276H209.7V93.7661H211.439V92.0276Z" fill="#E0E0E0"/>
<path d="M215.719 92.0276H213.98V93.7661H215.719V92.0276Z" fill="#E0E0E0"/>
<path d="M220.077 91.949H218.339V93.6875H220.077V91.949Z" fill="#E0E0E0"/>
<path d="M224.278 92.0276H222.539V93.7661H224.278V92.0276Z" fill="#E0E0E0"/>
<path d="M223.982 39.3382C225.3 31.138 219.721 23.4219 211.52 22.1038C203.32 20.7857 195.604 26.3647 194.286 34.5649C192.968 42.765 198.547 50.4811 206.747 51.7992C214.947 53.1174 222.663 47.5383 223.982 39.3382Z" fill="#E0E0E0"/>
<path d="M209.288 50.4031C216.805 50.4031 222.898 44.3098 222.898 36.7933C222.898 29.2769 216.805 23.1836 209.288 23.1836C201.772 23.1836 195.679 29.2769 195.679 36.7933C195.679 44.3098 201.772 50.4031 209.288 50.4031Z" fill="white"/>
<path d="M195.54 35.9771C195.54 35.9771 195.544 36.0525 195.545 36.1993C195.544 36.3506 195.543 36.5673 195.542 36.8506C195.541 36.9939 195.54 37.1541 195.539 37.3302C195.546 37.5063 195.57 37.6982 195.587 37.9065C195.606 38.1149 195.627 38.3391 195.649 38.5786C195.661 38.8197 195.734 39.0677 195.778 39.3355C195.948 40.4069 196.36 41.6871 197.051 43.061C197.749 44.4294 198.791 45.8802 200.258 47.1495C200.443 47.3067 200.629 47.4655 200.818 47.6267C201.015 47.7755 201.225 47.9143 201.431 48.0612C201.642 48.2005 201.841 48.3662 202.069 48.4882C202.295 48.6152 202.523 48.7437 202.753 48.8731C202.869 48.9376 202.985 49.0021 203.102 49.067C203.224 49.1206 203.347 49.1747 203.47 49.2292C203.716 49.3379 203.965 49.4475 204.217 49.5586C204.471 49.6617 204.739 49.7347 205.004 49.8254C205.272 49.9048 205.533 50.0184 205.814 50.066C206.371 50.1825 206.932 50.3358 207.521 50.378C207.813 50.4072 208.106 50.4553 208.404 50.4702C208.703 50.4742 209.004 50.4787 209.307 50.4826C209.458 50.4821 209.611 50.493 209.763 50.4831C209.915 50.4697 210.067 50.4563 210.221 50.4429C210.527 50.4127 210.837 50.3973 211.145 50.3547C211.755 50.2307 212.385 50.1498 212.992 49.942L213.454 49.8061C213.531 49.7828 213.609 49.7624 213.685 49.7361L213.91 49.6454C214.21 49.5244 214.512 49.4018 214.815 49.2793C215.112 49.1424 215.399 48.9792 215.695 48.8295C215.839 48.7496 215.993 48.6831 216.132 48.5919L216.552 48.3186C217.129 47.9758 217.643 47.5334 218.179 47.1098C218.425 46.8727 218.672 46.6342 218.92 46.3946L219.106 46.2145L219.275 46.0181L219.614 45.6223L219.953 45.225L220.256 44.796L220.559 44.364L220.711 44.1472C220.757 44.0723 220.798 43.994 220.843 43.9171C221.017 43.6091 221.191 43.2996 221.367 42.9891C221.511 42.6627 221.656 42.3358 221.801 42.007C222.334 40.6722 222.668 39.2398 222.784 37.7667C222.861 36.2911 222.718 34.8264 222.365 33.4331C222.264 33.0884 222.163 32.7447 222.063 32.4024C221.929 32.0716 221.796 31.7418 221.664 31.4139L221.564 31.1684L221.441 30.9338L221.198 30.4656L220.954 30.0003L220.669 29.5618L220.385 29.1249L220.242 28.9081L220.081 28.7052C219.866 28.4354 219.653 28.1666 219.44 27.8992C218.964 27.4092 218.512 26.9033 217.985 26.4876L217.604 26.1618C217.478 26.0526 217.335 25.9668 217.201 25.8686C216.928 25.6816 216.665 25.4822 216.389 25.3076C216.103 25.1464 215.82 24.9857 215.538 24.826L215.328 24.7065C215.255 24.6708 215.18 24.6405 215.107 24.6068L214.667 24.4119C214.093 24.1262 213.479 23.964 212.89 23.7611C212.59 23.6783 212.284 23.6227 211.985 23.5528C211.835 23.5196 211.685 23.4863 211.536 23.4531C211.386 23.4233 211.234 23.4144 211.084 23.3941C210.783 23.3589 210.484 23.3236 210.187 23.2884C209.889 23.2646 209.592 23.2736 209.299 23.2646C208.709 23.2294 208.134 23.3083 207.566 23.3509C207.28 23.3613 207.007 23.4397 206.731 23.4834C206.457 23.5389 206.181 23.5761 205.915 23.6456C205.652 23.7224 205.391 23.7988 205.132 23.8742C205.002 23.9119 204.873 23.9496 204.745 23.9868C204.621 24.0359 204.498 24.085 204.375 24.1336C204.129 24.2323 203.887 24.3295 203.647 24.4263C203.404 24.518 203.186 24.6559 202.958 24.7665C202.734 24.8851 202.508 24.9947 202.293 25.1167C202.085 25.2516 201.88 25.3845 201.676 25.5165C200.056 26.5834 198.834 27.8854 197.962 29.1512C197.097 30.4224 196.522 31.6381 196.213 32.6777C196.134 32.9371 196.03 33.1737 195.986 33.4113C195.933 33.6459 195.883 33.8651 195.837 34.0695C195.793 34.2738 195.744 34.4608 195.714 34.6344C195.692 34.809 195.672 34.9677 195.655 35.1101C195.619 35.3908 195.591 35.6056 195.572 35.7559C195.554 35.9027 195.54 35.9771 195.54 35.9771C195.54 35.9771 195.544 35.9022 195.558 35.7559C195.574 35.6051 195.596 35.3893 195.626 35.1071C195.641 34.9643 195.659 34.8046 195.678 34.629C195.705 34.4544 195.752 34.2664 195.795 34.0611C195.839 33.8557 195.886 33.635 195.937 33.3989C195.979 33.1603 196.081 32.9217 196.158 32.6608C196.46 31.6138 197.03 30.3882 197.894 29.104C198.764 27.8258 199.989 26.5095 201.618 25.4282C201.822 25.2948 202.029 25.1598 202.239 25.0234C202.455 24.8994 202.683 24.7878 202.908 24.6678C203.137 24.5557 203.357 24.4158 203.602 24.3226C203.844 24.2244 204.088 24.1252 204.335 24.0255C204.459 23.9759 204.583 23.9263 204.708 23.8762C204.837 23.838 204.967 23.7998 205.098 23.7611C205.358 23.6842 205.622 23.6069 205.888 23.5285C206.156 23.4581 206.434 23.4194 206.71 23.3628C206.988 23.3177 207.265 23.2383 207.553 23.2269C208.125 23.1823 208.706 23.1014 209.301 23.1357C209.598 23.1441 209.898 23.1342 210.198 23.158C210.498 23.1927 210.8 23.2274 211.104 23.2626C211.256 23.283 211.41 23.2919 211.561 23.3217C211.712 23.3549 211.863 23.3876 212.014 23.4214C212.317 23.4913 212.625 23.5473 212.928 23.6307C213.524 23.834 214.144 23.9977 214.725 24.2849L215.17 24.4813C215.244 24.515 215.32 24.5453 215.393 24.5815L215.606 24.702C215.891 24.8627 216.178 25.0244 216.466 25.1871C216.746 25.3632 217.013 25.5641 217.289 25.7531C217.424 25.8523 217.568 25.9386 217.696 26.0487L218.081 26.3775C218.615 26.7971 219.072 27.3075 219.553 27.803C219.769 28.0733 219.985 28.3447 220.202 28.6175L220.365 28.8223L220.51 29.0415L220.797 29.483L221.085 29.9264L221.333 30.3966L221.579 30.8698L221.703 31.1069L221.804 31.3549C221.938 31.6867 222.073 32.02 222.208 32.3543C222.309 32.7005 222.411 33.0477 222.514 33.3964C222.872 34.8051 223.017 36.2866 222.938 37.7786C222.821 39.2676 222.483 40.7164 221.944 42.066C221.796 42.3983 221.65 42.7292 221.504 43.0585C221.326 43.3725 221.15 43.6849 220.974 43.9964C220.929 44.0738 220.887 44.1527 220.84 44.2286L220.686 44.4478L220.38 44.8843L220.073 45.3178L219.73 45.7195L219.387 46.1193L219.216 46.3177L219.027 46.4992C218.776 46.7413 218.527 46.9819 218.278 47.2214C217.737 47.6495 217.216 48.0954 216.632 48.4416L216.207 48.7174C216.067 48.8096 215.912 48.8766 215.765 48.9569C215.466 49.1082 215.176 49.2724 214.876 49.4103C214.569 49.5338 214.263 49.6568 213.96 49.7788L213.733 49.8701C213.656 49.8964 213.577 49.9167 213.499 49.9405L213.033 50.0769C212.419 50.2857 211.783 50.3671 211.166 50.4906C210.854 50.5332 210.541 50.5486 210.232 50.5784C210.077 50.5917 209.923 50.6046 209.77 50.6175C209.616 50.6275 209.462 50.616 209.309 50.6165C209.003 50.6116 208.7 50.6066 208.398 50.6017C208.097 50.5858 207.801 50.5372 207.506 50.5064C206.911 50.4623 206.346 50.307 205.785 50.1875C205.5 50.1384 205.237 50.0238 204.967 49.943C204.701 49.8507 204.43 49.7758 204.174 49.6712C203.92 49.5586 203.669 49.4475 203.421 49.3369C203.296 49.2818 203.173 49.2268 203.05 49.1722C202.932 49.1062 202.815 49.0408 202.699 48.9758C202.467 48.8443 202.238 48.7144 202.01 48.5854C201.78 48.4614 201.581 48.2938 201.368 48.1524C201.16 48.0036 200.95 47.8632 200.751 47.7125C200.561 47.5498 200.374 47.3886 200.188 47.2299C198.715 45.9452 197.671 44.4795 196.976 43.0987C196.287 41.7128 195.882 40.4233 195.72 39.3454C195.678 39.0766 195.608 38.8266 195.598 38.5846C195.578 38.344 195.56 38.1188 195.543 37.9095C195.528 37.7002 195.506 37.5078 195.502 37.3312C195.506 37.1546 195.509 36.9944 195.513 36.8506C195.52 36.5673 195.526 36.3501 195.53 36.1988C195.534 36.052 195.54 35.9771 195.54 35.9771Z" fill="#EBEBEB"/>
<path d="M215.488 37.1661C215.488 37.1775 215.317 37.1884 215.004 37.1978C214.662 37.2048 214.215 37.2137 213.674 37.2246C212.545 37.24 210.971 37.2509 209.196 37.2539H209.108V37.1661C209.108 36.8819 209.107 36.5823 209.107 36.2783C209.107 34.2948 209.117 32.4997 209.133 31.2002C209.145 30.5738 209.154 30.0554 209.161 29.6616C209.172 29.298 209.184 29.0972 209.196 29.0972C209.208 29.0972 209.22 29.298 209.23 29.6616C209.237 30.0554 209.247 30.5738 209.259 31.2002C209.275 32.4997 209.285 34.2948 209.285 36.2783C209.284 36.5823 209.284 36.8819 209.284 37.1661L209.197 37.0783C210.972 37.0813 212.546 37.0917 213.675 37.1076C214.216 37.1185 214.663 37.1274 215.005 37.1344C215.316 37.1443 215.488 37.1547 215.488 37.1661Z" fill="#E0E0E0"/>
<path d="M209.238 25.3032C209.287 25.3032 209.326 25.8181 209.326 26.453C209.326 27.0883 209.287 27.6032 209.238 27.6032C209.189 27.6032 209.149 27.0883 209.149 26.453C209.149 25.8181 209.189 25.3032 209.238 25.3032Z" fill="#E0E0E0"/>
<path d="M215.476 27.1505C215.51 27.1852 215.18 27.5721 214.737 28.0145C214.294 28.4574 213.907 28.7877 213.873 28.7535C213.838 28.7188 214.169 28.3319 214.612 27.8895C215.054 27.4466 215.441 27.1157 215.476 27.1505Z" fill="#E0E0E0"/>
<path d="M219.123 31.1345C219.145 31.1792 218.711 31.4406 218.18 31.764C217.647 32.0869 217.215 32.3517 217.186 32.3115C217.158 32.2729 217.548 31.9395 218.087 31.6127C218.626 31.2848 219.102 31.0919 219.123 31.1345Z" fill="#E0E0E0"/>
<path d="M221.261 37.0473C221.261 37.0964 220.559 37.1361 219.693 37.1361C218.827 37.1361 218.125 37.0964 218.125 37.0473C218.125 36.9982 218.827 36.9585 219.693 36.9585C220.559 36.9585 221.261 36.9982 221.261 37.0473Z" fill="#E0E0E0"/>
<path d="M199.515 36.8381C199.515 36.8867 198.852 36.9268 198.034 36.9268C197.216 36.9268 196.553 36.8872 196.553 36.8381C196.553 36.7889 197.216 36.7493 198.034 36.7493C198.852 36.7493 199.515 36.7889 199.515 36.8381Z" fill="#E0E0E0"/>
<path d="M201.053 41.2402C201.086 41.2769 200.585 41.7317 199.934 42.2555C199.284 42.7793 198.733 43.1721 198.704 43.1329C198.675 43.0937 199.176 42.6394 199.823 42.1176C200.471 41.5958 201.021 41.2035 201.053 41.2402Z" fill="#E0E0E0"/>
<path d="M204.847 44.6444C204.888 44.6741 204.517 45.2455 204.085 45.9647C203.653 46.6829 203.323 47.2791 203.277 47.2568C203.234 47.2365 203.496 46.5996 203.933 45.873C204.37 45.1463 204.81 44.6161 204.847 44.6444Z" fill="#E0E0E0"/>
<path d="M209.238 49.0356C209.189 49.0356 209.149 48.3333 209.149 47.4673C209.149 46.6013 209.189 45.8989 209.238 45.8989C209.287 45.8989 209.326 46.6008 209.326 47.4673C209.326 48.3333 209.287 49.0356 209.238 49.0356Z" fill="#E0E0E0"/>
<path d="M215.998 46.9458C215.973 46.985 215.359 46.6323 214.704 46.06C214.048 45.4896 213.615 44.9301 213.65 44.8998C213.689 44.8661 214.175 45.3651 214.821 45.9265C215.465 46.49 216.026 46.9032 215.998 46.9458Z" fill="#E0E0E0"/>
<path d="M219.17 43.5295C219.135 43.5642 218.67 43.155 218.131 42.6164C217.592 42.0772 217.183 41.6125 217.218 41.5777C217.253 41.543 217.717 41.9517 218.256 42.4909C218.795 43.03 219.204 43.4948 219.17 43.5295Z" fill="#E0E0E0"/>
<path d="M200.909 32.6215C200.874 32.6562 200.464 32.3015 199.993 31.8303C199.521 31.3586 199.167 30.9484 199.202 30.9137C199.236 30.879 199.646 31.2332 200.118 31.7048C200.589 32.176 200.943 32.5867 200.909 32.6215Z" fill="#E0E0E0"/>
<path d="M204.824 28.4974C204.79 28.5321 204.386 28.1864 203.924 27.7256C203.46 27.2648 203.113 26.8631 203.148 26.8283C203.182 26.7936 203.585 27.1393 204.049 27.6001C204.511 28.0609 204.859 28.4627 204.824 28.4974Z" fill="#E0E0E0"/>
<path d="M197.102 36.9274C197.102 29.6828 202.764 23.7625 209.903 23.3439C209.635 23.328 209.366 23.3181 209.094 23.3181C201.578 23.3181 195.484 29.4115 195.484 36.9279C195.484 44.4442 201.578 50.5371 209.094 50.5371C209.366 50.5371 209.635 50.5267 209.903 50.5113C202.763 50.0917 197.102 44.1719 197.102 36.9274Z" fill="#F5F5F5"/>
<path d="M57.6631 24.8064H19.3486V85.3561H57.6631V24.8064Z" fill="#E0E0E0"/>
<path d="M56.5001 25.9441H20.3809V84.361H56.5001V25.9441Z" fill="#F5F5F5"/>
<path d="M52.8428 29.3469H38.4404V51.6957H52.8428V29.3469Z" fill="white"/>
<path d="M46.4074 31.3228C46.4074 31.6199 46.1668 31.8605 45.8697 31.8605C45.5726 31.8605 45.332 31.6199 45.332 31.3228C45.332 31.0257 45.5726 30.7852 45.8697 30.7852C46.1668 30.7852 46.4074 31.0257 46.4074 31.3228Z" fill="#E0E0E0"/>
<path d="M38.3281 57.9155H23.9258V80.2643H38.3281V57.9155Z" fill="white"/>
<path d="M31.8927 59.8912C31.8927 60.1883 31.6522 60.4288 31.355 60.4288C31.0579 60.4288 30.8174 60.1883 30.8174 59.8912C30.8174 59.5946 31.0579 59.3535 31.355 59.3535C31.6522 59.354 31.8927 59.5946 31.8927 59.8912Z" fill="#E0E0E0"/>
<path d="M36.0308 29.3469H23.7305V46.7987H36.0308V29.3469Z" fill="white"/>
<path d="M30.3264 30.932C30.3264 31.1785 30.1265 31.3784 29.88 31.3784C29.6335 31.3784 29.4336 31.1785 29.4336 30.932C29.4336 30.6855 29.6335 30.4856 29.88 30.4856C30.1265 30.4856 30.3264 30.6855 30.3264 30.932Z" fill="#E0E0E0"/>
<path d="M53.3165 58.2231L40.1621 59.707L40.9843 66.9952L54.1386 65.5112L53.3165 58.2231Z" fill="white"/>
<path d="M47.3501 60.3472C47.3501 60.5986 47.1463 60.8025 46.8948 60.8025C46.6433 60.8025 46.4395 60.5986 46.4395 60.3472C46.4395 60.0957 46.6433 59.8918 46.8948 59.8918C47.1463 59.8914 47.3501 60.0957 47.3501 60.3472Z" fill="#E0E0E0"/>
<path d="M36.4057 63.0129C36.4057 63.0719 34.144 63.12 31.355 63.12C28.565 63.12 26.3037 63.0719 26.3037 63.0129C26.3037 62.9539 28.565 62.9058 31.355 62.9058C34.144 62.9058 36.4057 62.9539 36.4057 63.0129Z" fill="#E0E0E0"/>
<path d="M36.4057 64.6123C36.4057 64.6718 34.144 64.7199 31.355 64.7199C28.565 64.7199 26.3037 64.6718 26.3037 64.6123C26.3037 64.5532 28.565 64.5051 31.355 64.5051C34.144 64.5051 36.4057 64.5527 36.4057 64.6123Z" fill="#E0E0E0"/>
<path d="M36.4057 66.2109C36.4057 66.2699 34.144 66.318 31.355 66.318C28.565 66.318 26.3037 66.2699 26.3037 66.2109C26.3037 66.1514 28.565 66.1038 31.355 66.1038C34.144 66.1033 36.4057 66.1514 36.4057 66.2109Z" fill="#E0E0E0"/>
<path d="M36.4057 67.8095C36.4057 67.8691 34.144 67.9167 31.355 67.9167C28.565 67.9167 26.3037 67.8686 26.3037 67.8095C26.3037 67.7505 28.565 67.7024 31.355 67.7024C34.144 67.7024 36.4057 67.7505 36.4057 67.8095Z" fill="#E0E0E0"/>
<path d="M36.4057 69.4087C36.4057 69.4682 34.144 69.5163 31.355 69.5163C28.565 69.5163 26.3037 69.4682 26.3037 69.4087C26.3037 69.3496 28.565 69.3015 31.355 69.3015C34.144 69.3015 36.4057 69.3496 36.4057 69.4087Z" fill="#E0E0E0"/>
<path d="M36.4057 71.0078C36.4057 71.0673 34.144 71.1149 31.355 71.1149C28.565 71.1149 26.3037 71.0668 26.3037 71.0078C26.3037 70.9488 28.565 70.9006 31.355 70.9006C34.144 70.9001 36.4057 70.9483 36.4057 71.0078Z" fill="#E0E0E0"/>
<path d="M36.4057 72.6069C36.4057 72.6664 34.144 72.714 31.355 72.714C28.565 72.714 26.3037 72.6659 26.3037 72.6069C26.3037 72.5479 28.565 72.4998 31.355 72.4998C34.144 72.4993 36.4057 72.5474 36.4057 72.6069Z" fill="#E0E0E0"/>
<path d="M52.0839 61.3003C52.0923 61.3588 49.8593 61.7223 47.0976 62.1122C44.3349 62.5021 42.089 62.7704 42.0811 62.7119C42.0726 62.6533 44.3051 62.2898 47.0679 61.8999C49.8296 61.5101 52.0755 61.2417 52.0839 61.3003Z" fill="#E0E0E0"/>
<path d="M52.2841 62.2998C52.2925 62.3583 50.0595 62.7219 47.2978 63.1117C44.5351 63.5016 42.2892 63.7699 42.2813 63.7114C42.2733 63.6528 44.5053 63.2893 47.2681 62.8994C50.0293 62.5096 52.2757 62.2412 52.2841 62.2998Z" fill="#E0E0E0"/>
<path d="M52.4677 63.2241C52.4761 63.2826 50.2431 63.6467 47.4814 64.036C44.7187 64.4259 42.4733 64.6942 42.4649 64.6357C42.4564 64.5772 44.6889 64.2136 47.4517 63.8237C50.2129 63.4339 52.4593 63.1656 52.4677 63.2241Z" fill="#E0E0E0"/>
<path d="M51.5765 34.0222C51.5765 34.0817 49.0216 34.1293 45.8705 34.1293C42.7185 34.1293 40.1641 34.0812 40.1641 34.0222C40.1641 33.9632 42.7185 33.915 45.8705 33.915C49.0211 33.915 51.5765 33.9632 51.5765 34.0222Z" fill="#E0E0E0"/>
<path d="M51.5765 34.9729C51.5765 35.0319 49.0216 35.08 45.8705 35.08C42.7185 35.08 40.1641 35.0319 40.1641 34.9729C40.1641 34.9138 42.7185 34.8657 45.8705 34.8657C49.0211 34.8657 51.5765 34.9133 51.5765 34.9729Z" fill="#E0E0E0"/>
<path d="M51.5765 35.9235C51.5765 35.9831 49.0216 36.0307 45.8705 36.0307C42.7185 36.0307 40.1641 35.9826 40.1641 35.9235C40.1641 35.8645 42.7185 35.8164 45.8705 35.8164C49.0211 35.8159 51.5765 35.864 51.5765 35.9235Z" fill="#E0E0E0"/>
<path d="M51.5765 36.8742C51.5765 36.9333 49.0216 36.9814 45.8705 36.9814C42.7185 36.9814 40.1641 36.9333 40.1641 36.8742C40.1641 36.8152 42.7185 36.7671 45.8705 36.7671C49.0211 36.7671 51.5765 36.8152 51.5765 36.8742Z" fill="#E0E0E0"/>
<path d="M51.5765 37.8249C51.5765 37.8839 49.0216 37.932 45.8705 37.932C42.7185 37.932 40.1641 37.8839 40.1641 37.8249C40.1641 37.7659 42.7185 37.7178 45.8705 37.7178C49.0211 37.7178 51.5765 37.7659 51.5765 37.8249Z" fill="#E0E0E0"/>
<path d="M51.5765 38.7761C51.5765 38.8351 49.0216 38.8832 45.8705 38.8832C42.7185 38.8832 40.1641 38.8351 40.1641 38.7761C40.1641 38.7171 42.7185 38.6689 45.8705 38.6689C49.0211 38.6689 51.5765 38.7171 51.5765 38.7761Z" fill="#E0E0E0"/>
<path d="M51.5765 39.7268C51.5765 39.7858 49.0216 39.8339 45.8705 39.8339C42.7185 39.8339 40.1641 39.7858 40.1641 39.7268C40.1641 39.6677 42.7185 39.6196 45.8705 39.6196C49.0211 39.6196 51.5765 39.6677 51.5765 39.7268Z" fill="#E0E0E0"/>
<path d="M51.5765 42.5481C51.5765 42.6076 49.0216 42.6552 45.8705 42.6552C42.7185 42.6552 40.1641 42.6071 40.1641 42.5481C40.1641 42.489 42.7185 42.4409 45.8705 42.4409C49.0211 42.4409 51.5765 42.489 51.5765 42.5481Z" fill="#E0E0E0"/>
<path d="M51.6566 43.5525C51.6566 43.6115 49.1017 43.6596 45.9506 43.6596C42.7985 43.6596 40.2441 43.6115 40.2441 43.5525C40.2441 43.4934 42.7985 43.4453 45.9506 43.4453C49.1017 43.4448 51.6566 43.4929 51.6566 43.5525Z" fill="#E0E0E0"/>
<path d="M51.7367 44.5564C51.7367 44.6159 49.1818 44.6635 46.0307 44.6635C42.8786 44.6635 40.3242 44.6154 40.3242 44.5564C40.3242 44.4973 42.8786 44.4492 46.0307 44.4492C49.1818 44.4492 51.7367 44.4973 51.7367 44.5564Z" fill="#E0E0E0"/>
<path d="M51.8168 45.5605C51.8168 45.6195 49.2619 45.6676 46.1108 45.6676C42.9587 45.6676 40.4043 45.6195 40.4043 45.5605C40.4043 45.5015 42.9587 45.4534 46.1108 45.4534C49.2619 45.4534 51.8168 45.5015 51.8168 45.5605Z" fill="#E0E0E0"/>
<path d="M51.8968 46.5644C51.8968 46.6234 49.3419 46.6715 46.1909 46.6715C43.0388 46.6715 40.4844 46.6234 40.4844 46.5644C40.4844 46.5054 43.0388 46.4573 46.1909 46.4573C49.3419 46.4573 51.8968 46.5054 51.8968 46.5644Z" fill="#E0E0E0"/>
<path d="M51.9779 47.5688C51.9779 47.6283 49.423 47.6759 46.2719 47.6759C43.1198 47.6759 40.5654 47.6278 40.5654 47.5688C40.5654 47.5098 43.1198 47.4617 46.2719 47.4617C49.423 47.4617 51.9779 47.5098 51.9779 47.5688Z" fill="#E0E0E0"/>
<path d="M238.65 239.242C238.65 239.313 187.539 239.371 124.505 239.371C61.449 239.371 10.3486 239.313 10.3486 239.242C10.3486 239.17 61.4485 239.113 124.505 239.113C187.539 239.113 238.65 239.171 238.65 239.242Z" fill="#263238"/>
<path d="M177.205 186.959H72.98C70.4504 186.959 68.3994 184.908 68.3994 182.379V30.6042C68.3994 28.0741 70.4504 26.0237 72.98 26.0237H177.205C179.735 26.0237 181.786 28.0746 181.786 30.6042V182.379C181.786 184.908 179.735 186.959 177.205 186.959Z" fill="#E0E0E0"/>
<path d="M177.205 186.96C177.205 186.96 177.323 186.95 177.556 186.936C177.789 186.92 178.135 186.867 178.577 186.738C179.013 186.595 179.543 186.354 180.075 185.925C180.599 185.496 181.126 184.869 181.45 184.031C181.621 183.617 181.717 183.145 181.748 182.646C181.774 182.146 181.754 181.614 181.759 181.059C181.757 179.948 181.755 178.732 181.754 177.416C181.751 174.782 181.747 171.743 181.744 168.323C181.735 154.639 181.723 134.84 181.709 110.376C181.705 98.1439 181.701 84.7449 181.696 70.3609C181.694 63.1689 181.692 55.7304 181.69 48.0687C181.689 44.2376 181.689 40.3509 181.688 36.4107C181.685 34.4416 181.682 32.4596 181.679 30.4646C181.66 29.486 181.285 28.5263 180.668 27.7629C180.038 27.0105 179.166 26.451 178.197 26.2397C177.713 26.1296 177.221 26.1197 176.71 26.1261C176.203 26.1261 175.695 26.1261 175.187 26.1261C174.169 26.1261 173.15 26.1266 172.126 26.1266C170.08 26.1271 168.021 26.1271 165.951 26.1276C161.809 26.1281 157.621 26.1286 153.388 26.1291C144.923 26.1301 136.278 26.1311 127.479 26.1321C109.879 26.1326 91.6572 26.1336 72.9937 26.1341C71.8519 26.1301 70.7221 26.5805 69.9022 27.3686C69.0753 28.1488 68.5694 29.2529 68.5213 30.3843C68.4985 32.6927 68.5149 35.013 68.5099 37.3154C68.5099 41.9247 68.5094 46.5038 68.5094 51.0491C68.5089 60.1403 68.5084 69.0981 68.5079 77.9001C68.5059 95.5041 68.5034 112.484 68.5015 128.66C68.498 144.835 68.4945 160.206 68.491 174.591C68.4906 176.389 68.4901 178.172 68.4896 179.939C68.4935 180.821 68.4791 181.706 68.4985 182.574C68.5273 183.433 68.8224 184.272 69.3114 184.967C69.8015 185.665 70.4909 186.209 71.2686 186.534C71.66 186.692 72.0707 186.794 72.4868 186.846C72.902 186.882 73.3305 186.874 73.7566 186.874C75.4589 186.875 77.1453 186.875 78.8143 186.876C85.4914 186.878 91.8993 186.881 98.0154 186.883C122.48 186.898 142.276 186.91 155.959 186.917C162.798 186.926 168.11 186.933 171.713 186.938C173.512 186.943 174.885 186.947 175.81 186.949C176.732 186.952 177.205 186.96 177.205 186.96C177.205 186.96 176.732 186.967 175.809 186.971C174.885 186.973 173.512 186.977 171.712 186.982C168.109 186.986 162.798 186.993 155.959 187.003C142.276 187.011 122.479 187.023 98.015 187.037C91.8988 187.04 85.491 187.042 78.8138 187.044C77.1448 187.045 75.4584 187.045 73.7561 187.046C73.331 187.045 72.905 187.055 72.4675 187.017C72.035 186.964 71.6079 186.858 71.2017 186.694C70.3932 186.356 69.677 185.792 69.1676 185.067C68.6587 184.346 68.3517 183.473 68.3209 182.58C68.3006 181.698 68.315 180.824 68.31 179.938C68.3095 178.171 68.309 176.388 68.3085 174.59C68.3055 160.205 68.3021 144.834 68.2986 128.659C68.2966 112.483 68.2946 95.5027 68.2922 77.8991C68.2917 69.0971 68.2912 60.1393 68.2907 51.0482C68.2907 46.5028 68.2902 41.9237 68.2902 37.3144C68.2951 35.0075 68.2788 32.7026 68.3016 30.3729C68.3522 29.186 68.8834 28.0268 69.7504 27.2089C70.61 26.3826 71.7974 25.9094 72.9932 25.9138C91.6567 25.9143 109.879 25.9153 127.478 25.9163C136.278 25.9173 144.922 25.9183 153.388 25.9188C157.621 25.9193 161.809 25.9198 165.95 25.9208C168.02 25.9213 170.079 25.9213 172.126 25.9218C173.149 25.9218 174.169 25.9223 175.186 25.9223C175.694 25.9223 176.202 25.9223 176.709 25.9223C177.211 25.9163 177.738 25.9258 178.241 26.0413C179.253 26.2635 180.166 26.8478 180.823 27.6345C181.467 28.4315 181.858 29.4359 181.879 30.4577C181.881 32.4546 181.883 34.4391 181.885 36.4102C181.884 40.3499 181.883 44.2371 181.882 48.0682C181.88 55.7304 181.878 63.1684 181.877 70.3604C181.872 84.7444 181.867 98.1434 181.863 110.376C181.848 134.84 181.836 154.638 181.829 168.322C181.825 171.743 181.822 174.781 181.819 177.415C181.817 178.732 181.815 179.947 181.814 181.059C181.807 181.615 181.826 182.143 181.797 182.648C181.765 183.152 181.665 183.628 181.492 184.047C181.161 184.891 180.627 185.521 180.096 185.951C179.558 186.379 179.024 186.619 178.584 186.759C178.138 186.885 177.79 186.934 177.557 186.947C177.323 186.958 177.205 186.96 177.205 186.96Z" fill="#263238"/>
<g opacity="0.3">
<path d="M78.292 183.323H173.455L172.411 163.529V152.994L78.292 167.7V183.323Z" fill="black"/>
</g>
<g opacity="0.3">
<path d="M153.704 26.3525L155.866 32.7663V35.9085H153.428L153.704 26.3525Z" fill="black"/>
</g>
<path d="M73.4561 32.4719V180.307H150.578H176.728V32.4719H73.4561Z" fill="white"/>
<path d="M176.728 153.872C176.728 153.872 176.723 153.681 176.721 153.304C176.719 152.925 176.716 152.363 176.713 151.624C176.709 150.14 176.704 147.942 176.698 145.08C176.691 139.35 176.682 130.96 176.67 120.31C176.661 99.0036 176.647 68.6544 176.631 32.4717L176.728 32.5689C146.216 32.5723 110.906 32.5768 73.4553 32.5813L73.5649 32.4717C73.5649 32.5133 73.5649 32.558 73.5649 32.6031C73.5545 89.4338 73.5451 141.333 73.5377 180.307L73.4553 180.225C97.2127 180.24 116.493 180.252 129.835 180.261C136.504 180.27 141.69 180.278 145.209 180.283C146.967 180.288 148.308 180.292 149.212 180.295C150.115 180.299 150.577 180.307 150.577 180.307C150.577 180.307 150.115 180.315 149.212 180.32C148.308 180.322 146.966 180.326 145.209 180.332C141.69 180.337 136.504 180.344 129.835 180.354C116.492 180.362 97.2127 180.375 73.4553 180.39H73.373V180.308C73.3655 141.334 73.3561 89.4348 73.3457 32.6041C73.3457 32.559 73.3457 32.5138 73.3457 32.4727V32.363H73.4553C110.906 32.3675 146.216 32.372 176.728 32.3754H176.825V32.4727C176.809 68.6554 176.795 99.0046 176.786 120.311C176.774 130.962 176.765 139.352 176.758 145.081C176.752 147.943 176.747 150.141 176.743 151.625C176.74 152.364 176.737 152.926 176.735 153.305C176.734 153.681 176.728 153.872 176.728 153.872Z" fill="#263238"/>
<path d="M153.704 37.1709H101.451V25.1528C101.451 23.4203 102.856 22.0151 104.589 22.0151H150.567C152.299 22.0151 153.704 23.4198 153.704 25.1528V37.1709Z" fill="#263238"/>
<path d="M134.227 16.3152H120.928L122.482 27.7148H132.5L134.227 16.3152Z" fill="#263238"/>
<path d="M150.347 22.0149C150.347 22.0754 140.152 22.1245 127.578 22.1245C115 22.1245 104.808 22.0754 104.808 22.0149C104.808 21.9544 115 21.9053 127.578 21.9053C140.152 21.9053 150.347 21.9544 150.347 22.0149Z" fill="#455A64"/>
<path d="M102.621 34.7257C102.561 34.7257 102.512 32.582 102.512 29.9378C102.512 27.2926 102.561 25.1494 102.621 25.1494C102.682 25.1494 102.731 27.2926 102.731 29.9378C102.731 32.582 102.682 34.7257 102.621 34.7257Z" fill="#455A64"/>
<path d="M152.012 33.5921C151.951 33.5921 151.902 31.6458 151.902 29.2451C151.902 26.844 151.951 24.8982 152.012 24.8982C152.072 24.8982 152.122 26.844 152.122 29.2451C152.122 31.6458 152.072 33.5921 152.012 33.5921Z" fill="#455A64"/>
<path d="M149.493 35.6077C149.493 35.6682 139.028 35.7173 126.121 35.7173C113.21 35.7173 102.747 35.6682 102.747 35.6077C102.747 35.5472 113.21 35.498 126.121 35.498C139.027 35.498 149.493 35.5472 149.493 35.6077Z" fill="#455A64"/>
<path d="M163.653 60.844H106.62V64.3497H163.653V60.844Z" fill="#E0E0E0"/>
<path d="M132.494 68.0051H106.62V71.4865H132.494V68.0051Z" fill="#E0E0E0"/>
<path d="M98.9005 72.5331H89.5459C88.488 72.5331 87.6299 71.6755 87.6299 70.617V61.2625C87.6299 60.2045 88.4875 59.3464 89.5459 59.3464H98.9005C99.9585 59.3464 100.817 60.204 100.817 61.2625V70.617C100.817 71.6755 99.959 72.5331 98.9005 72.5331Z" fill="#5F9EA0"/>
<path d="M94.0345 69.0809L90.3408 66.2264L91.3368 64.9373L93.8138 66.8514L97.3364 62.7981L98.566 63.867L94.0345 69.0809Z" fill="white"/>
<path d="M163.653 82.55H106.62V86.0558H163.653V82.55Z" fill="#E0E0E0"/>
<path d="M132.494 89.7112H106.62V93.1926H132.494V89.7112Z" fill="#E0E0E0"/>
<path d="M98.9005 94.2389H89.5459C88.488 94.2389 87.6299 93.3813 87.6299 92.3229V82.9683C87.6299 81.9103 88.4875 81.0522 89.5459 81.0522H98.9005C99.9585 81.0522 100.817 81.9098 100.817 82.9683V92.3229C100.817 93.3813 99.959 94.2389 98.9005 94.2389Z" fill="#5F9EA0"/>
<path d="M94.0345 90.7867L90.3408 87.9323L91.3368 86.6431L93.8138 88.5572L97.3364 84.5039L98.566 85.5728L94.0345 90.7867Z" fill="white"/>
<path d="M163.653 105.806H106.62V109.312H163.653V105.806Z" fill="#E0E0E0"/>
<path d="M132.494 112.968H106.62V116.449H132.494V112.968Z" fill="#E0E0E0"/>
<path d="M163.653 127.307H106.62V130.813H163.653V127.307Z" fill="#E0E0E0"/>
<path d="M132.494 134.469H106.62V137.95H132.494V134.469Z" fill="#E0E0E0"/>
<path d="M98.9005 117.496H89.5459C88.488 117.496 87.6299 116.638 87.6299 115.579V106.225C87.6299 105.167 88.4875 104.309 89.5459 104.309H98.9005C99.9585 104.309 100.817 105.166 100.817 106.225V115.579C100.817 116.637 99.959 117.496 98.9005 117.496Z" fill="#5F9EA0"/>
<path d="M94.0345 114.044L90.3408 111.189L91.3368 109.9L93.8138 111.814L97.3364 107.761L98.566 108.829L94.0345 114.044Z" fill="white"/>
<path d="M99.0895 139.832H89.358C88.2822 139.832 87.4072 138.957 87.4072 137.88V128.149C87.4072 127.073 88.2827 126.198 89.358 126.198H99.0895C100.165 126.198 101.041 127.073 101.041 128.149V137.88C101.041 138.957 100.166 139.832 99.0895 139.832ZM89.3585 126.645C88.5292 126.645 87.8546 127.32 87.8546 128.149V137.88C87.8546 138.71 88.5292 139.385 89.3585 139.385H99.09C99.9193 139.385 100.594 138.71 100.594 137.88V128.149C100.594 127.32 99.9193 126.645 99.09 126.645H89.3585Z" fill="#5F9EA0"/>
<path d="M163.653 151.901H106.62V155.407H163.653V151.901Z" fill="#E0E0E0"/>
<path d="M132.494 159.062H106.62V162.544H132.494V159.062Z" fill="#E0E0E0"/>
<path d="M99.0895 164.426H89.358C88.2822 164.426 87.4072 163.551 87.4072 162.475V152.743C87.4072 151.668 88.2827 150.793 89.358 150.793H99.0895C100.165 150.793 101.041 151.668 101.041 152.743V162.475C101.041 163.551 100.166 164.426 99.0895 164.426ZM89.3585 151.24C88.5292 151.24 87.8546 151.914 87.8546 152.743V162.475C87.8546 163.304 88.5292 163.979 89.3585 163.979H99.09C99.9193 163.979 100.594 163.304 100.594 162.475V152.743C100.594 151.915 99.9193 151.24 99.09 151.24H89.3585Z" fill="#5F9EA0"/>
<path d="M156.619 47.2688H99.7559V52.137H156.619V47.2688Z" fill="#5F9EA0"/>
<path d="M109.568 227.542C109.568 227.542 107.856 215.774 107.999 208.927C108.142 202.08 107.99 194.377 107.99 194.377H108.779C108.779 194.377 108.245 207.968 108.826 216.165C109.407 224.355 110.938 227.542 110.938 227.542H109.568Z" fill="#263238"/>
<path d="M114.667 181.136C114.508 180.734 114.313 180.316 113.948 180.084C113.448 179.767 112.792 179.899 112.233 180.097C109.85 180.94 107.737 185.383 106.773 187.72L109.937 198.381C115.649 193.083 116.638 186.133 114.667 181.136Z" fill="#455A64"/>
<path d="M113.763 180.516C113.767 180.518 113.751 180.566 113.717 180.655C113.676 180.757 113.625 180.886 113.562 181.043C113.423 181.379 113.229 181.867 112.996 182.472C112.53 183.683 111.937 185.375 111.406 187.282C110.878 189.19 110.514 190.947 110.289 192.224C110.176 192.863 110.092 193.381 110.038 193.741C110.01 193.908 109.988 194.045 109.97 194.153C109.953 194.247 109.942 194.297 109.938 194.296C109.933 194.296 109.935 194.245 109.944 194.15C109.956 194.041 109.97 193.903 109.987 193.734C110.026 193.373 110.097 192.851 110.198 192.209C110.4 190.925 110.751 189.16 111.281 187.247C111.813 185.335 112.421 183.642 112.91 182.438C113.154 181.836 113.362 181.352 113.515 181.023C113.587 180.869 113.646 180.744 113.693 180.644C113.735 180.558 113.759 180.514 113.763 180.516Z" fill="#263238"/>
<path d="M108.531 200.937C105.566 199.299 103.818 196.215 103.654 192.831C103.54 190.465 104.267 188.144 105.091 185.923C105.162 185.731 105.247 185.523 105.426 185.422C105.675 185.281 105.986 185.406 106.234 185.548C108.389 186.775 109.695 189.114 110.39 191.495C110.852 193.079 111.091 194.749 110.878 196.386C110.664 198.022 109.728 199.803 108.531 200.937Z" fill="#455A64"/>
<path d="M108.531 200.938C108.527 200.938 108.523 200.888 108.52 200.795C108.518 200.687 108.516 200.551 108.512 200.386C108.511 200.03 108.497 199.517 108.467 198.883C108.41 197.615 108.244 195.867 107.917 193.957C107.588 192.048 107.189 190.338 106.895 189.102C106.75 188.497 106.631 188.002 106.543 187.638C106.506 187.477 106.476 187.345 106.452 187.239C106.433 187.147 106.424 187.099 106.429 187.098C106.433 187.097 106.45 187.143 106.478 187.233C106.508 187.336 106.546 187.466 106.593 187.625C106.69 187.967 106.825 188.464 106.984 189.08C107.301 190.311 107.716 192.02 108.045 193.936C108.374 195.85 108.526 197.606 108.559 198.879C108.577 199.515 108.577 200.03 108.562 200.386C108.555 200.552 108.55 200.687 108.545 200.795C108.541 200.889 108.536 200.938 108.531 200.938Z" fill="#263238"/>
<g opacity="0.3">
<path d="M108.531 200.937C105.566 199.299 103.818 196.215 103.654 192.831C103.54 190.465 104.267 188.144 105.091 185.923C105.162 185.731 105.247 185.523 105.426 185.422C105.675 185.281 105.986 185.406 106.234 185.548C108.389 186.775 109.695 189.114 110.39 191.495C110.852 193.079 111.091 194.749 110.878 196.386C110.664 198.022 109.728 199.803 108.531 200.937Z" fill="black"/>
</g>
<path d="M111.878 218.944C114.797 219.585 117.2 221.644 120.016 222.645C121.879 223.307 123.986 223.542 125.474 224.844C125.117 223.943 124.27 223.343 123.449 222.829C121.133 221.383 118.653 220.199 116.073 219.308C114.748 218.851 113.31 218.469 111.96 218.845" fill="#455A64"/>
<path d="M107.656 221.544C104.001 221.485 100.152 218.373 96.5137 218.187C100.321 216.958 105.773 217.962 107.656 221.544Z" fill="#455A64"/>
<path d="M109.086 212.776C110.942 212.106 112.98 212.075 114.93 212.378C116.88 212.681 118.762 213.306 120.635 213.928C120.447 212.66 119.42 211.706 118.455 210.862C117.729 210.227 116.998 209.588 116.164 209.101C115.331 208.615 114.381 208.285 113.417 208.326C112.254 208.375 111.147 208.975 110.384 209.854C109.622 210.733 109.212 211.618 109.086 212.776Z" fill="#455A64"/>
<path d="M101.583 208.739C99.282 206.774 95.7966 206.966 92.7715 207.035C97.4215 209.258 102.432 212.306 106.437 215.551C106.56 213.412 104.49 211.221 101.583 208.739Z" fill="#455A64"/>
<g opacity="0.3">
<path d="M101.583 208.739C99.282 206.774 95.7966 206.966 92.7715 207.035C97.4215 209.258 102.432 212.306 106.437 215.551C106.56 213.412 104.49 211.221 101.583 208.739Z" fill="black"/>
</g>
<path d="M110.12 206.027C111.277 205.256 112.447 204.52 113.659 203.97C114.87 203.42 116 202.543 117.169 201.91C118.686 201.088 120 199.947 121.392 198.929C122.785 197.911 124.314 196.991 126.018 196.722C124.628 196.435 123.177 196.591 121.808 196.965C120.44 197.339 119.14 197.924 117.847 198.507C116.052 199.315 114.226 200.144 112.758 201.456C111.291 202.769 109.965 204.064 110.12 206.027Z" fill="#455A64"/>
<path d="M119.948 221.544C119.946 221.548 119.906 221.531 119.833 221.494C119.746 221.449 119.641 221.395 119.514 221.329C119.239 221.183 118.843 220.965 118.346 220.715C118.099 220.585 117.826 220.449 117.53 220.311C117.237 220.165 116.919 220.021 116.581 219.875C115.906 219.587 115.153 219.286 114.33 219.09C113.511 218.889 112.694 218.845 111.986 219.031C111.628 219.118 111.292 219.203 110.992 219.328C110.691 219.45 110.424 219.592 110.19 219.74C109.722 220.034 109.394 220.347 109.182 220.572C108.97 220.799 108.867 220.938 108.858 220.931C108.854 220.929 108.877 220.891 108.923 220.823C108.969 220.755 109.043 220.658 109.144 220.538C109.347 220.3 109.669 219.972 110.139 219.662C110.374 219.507 110.645 219.357 110.95 219.228C111.255 219.095 111.6 219.004 111.957 218.914C112.681 218.716 113.53 218.759 114.36 218.963C115.195 219.162 115.953 219.469 116.629 219.765C116.967 219.915 117.285 220.063 117.577 220.213C117.873 220.357 118.144 220.498 118.388 220.633C118.881 220.894 119.27 221.124 119.538 221.284C119.661 221.359 119.762 221.421 119.845 221.471C119.916 221.515 119.951 221.54 119.948 221.544Z" fill="#263238"/>
<path d="M104.93 219.733C104.933 219.724 105.014 219.749 105.159 219.8C105.304 219.849 105.509 219.937 105.755 220.059C106.247 220.302 106.894 220.715 107.501 221.297C108.107 221.881 108.546 222.511 108.809 222.993C108.941 223.234 109.036 223.434 109.091 223.578C109.148 223.72 109.176 223.801 109.167 223.804C109.146 223.813 109.009 223.504 108.729 223.04C108.449 222.575 108.006 221.966 107.41 221.391C106.813 220.82 106.186 220.401 105.711 220.141C105.236 219.878 104.921 219.754 104.93 219.733Z" fill="#263238"/>
<path d="M96.8428 208.555C96.8443 208.551 96.8979 208.567 96.9985 208.601C97.1116 208.641 97.257 208.694 97.4375 208.758C97.6324 208.829 97.8636 208.915 98.1294 209.012C98.3993 209.113 98.6919 209.26 99.0232 209.406C100.339 210.004 102.09 211.009 103.782 212.438C104.617 213.164 105.371 213.903 106.001 214.624C106.63 215.346 107.162 216.026 107.577 216.619C107.994 217.21 108.311 217.703 108.528 218.044C108.63 218.204 108.713 218.334 108.778 218.435C108.834 218.525 108.862 218.574 108.858 218.577C108.855 218.579 108.82 218.536 108.756 218.451C108.692 218.366 108.599 218.239 108.485 218.072C108.255 217.74 107.927 217.256 107.502 216.673C107.079 216.089 106.542 215.418 105.912 214.704C105.279 213.99 104.527 213.26 103.698 212.538C102.017 211.118 100.285 210.107 98.984 209.49C98.6567 209.339 98.3675 209.186 98.1017 209.08C97.8398 208.974 97.6121 208.883 97.4197 208.805C97.2436 208.732 97.1007 208.672 96.9901 208.626C96.8919 208.584 96.8413 208.56 96.8428 208.555Z" fill="#263238"/>
<path d="M114.57 210.721C114.568 210.73 114.464 210.716 114.279 210.683C114.178 210.665 114.067 210.644 113.943 210.621C113.812 210.596 113.66 210.595 113.494 210.578C112.837 210.53 111.879 210.584 110.982 211.073C110.529 211.302 110.149 211.611 109.838 211.921C109.523 212.229 109.284 212.55 109.103 212.83C108.737 213.392 108.623 213.797 108.601 213.789C108.594 213.787 108.614 213.684 108.675 213.504C108.742 213.327 108.846 213.071 109.025 212.782C109.199 212.49 109.435 212.157 109.753 211.837C110.066 211.515 110.454 211.194 110.92 210.958C111.846 210.454 112.836 210.413 113.501 210.486C113.67 210.509 113.824 210.517 113.955 210.549C114.078 210.58 114.189 210.608 114.29 210.633C114.473 210.681 114.573 210.712 114.57 210.721Z" fill="#263238"/>
<path d="M118.244 199.323C118.246 199.327 118.198 199.357 118.106 199.409C118.001 199.466 117.865 199.539 117.698 199.63C117.343 199.821 116.833 200.103 116.213 200.469C115.596 200.839 114.869 201.293 114.096 201.846C113.324 202.401 112.496 203.045 111.731 203.827C111.354 204.221 111.006 204.633 110.764 205.079C110.524 205.539 110.296 205.984 110.085 206.41C109.663 207.263 109.319 208.048 109.07 208.722C108.822 209.397 108.667 209.959 108.576 210.35C108.482 210.741 108.448 210.962 108.435 210.959C108.431 210.959 108.436 210.903 108.45 210.798C108.463 210.692 108.485 210.537 108.525 210.339C108.602 209.943 108.744 209.373 108.982 208.691C109.221 208.008 109.559 207.215 109.977 206.357C110.185 205.928 110.412 205.481 110.65 205.02C110.901 204.553 111.254 204.136 111.636 203.737C112.411 202.945 113.246 202.3 114.025 201.748C114.806 201.198 115.541 200.75 116.166 200.389C116.794 200.033 117.312 199.762 117.674 199.585C117.846 199.503 117.985 199.436 118.094 199.385C118.191 199.341 118.242 199.319 118.244 199.323Z" fill="#263238"/>
<path d="M107.994 210.391C107.985 210.393 107.96 210.283 107.922 210.083C107.879 209.857 107.825 209.575 107.762 209.238C107.623 208.527 107.41 207.547 107.07 206.496C106.734 205.443 106.325 204.526 106.026 203.866C105.885 203.554 105.765 203.292 105.67 203.083C105.586 202.898 105.545 202.793 105.553 202.789C105.561 202.785 105.618 202.882 105.715 203.06C105.813 203.239 105.949 203.5 106.11 203.826C106.431 204.478 106.856 205.394 107.194 206.455C107.536 207.515 107.736 208.504 107.852 209.222C107.909 209.582 107.95 209.873 107.972 210.075C107.995 210.278 108.003 210.39 107.994 210.391Z" fill="#263238"/>
<path d="M107.729 202.075C107.637 200.694 107.188 199.351 106.54 198.128C104.766 194.774 101.431 192.287 97.71 191.543C100.435 195.605 103.16 199.666 105.885 203.728L106.941 205.902C107.661 204.72 107.821 203.455 107.729 202.075Z" fill="#455A64"/>
<path d="M119.456 226.087H100.447V229.456H119.456V226.087Z" fill="#5F9D9F"/>
<path d="M101.866 228.788L103.485 239.242H116.104L118.036 228.788H101.866Z" fill="#5F9D9F"/>
<path d="M119.446 229.46C119.446 229.497 115.158 229.527 109.87 229.527C104.58 229.527 100.293 229.497 100.293 229.46C100.293 229.424 104.58 229.393 109.87 229.393C115.158 229.393 119.446 229.423 119.446 229.46Z" fill="#263238"/>
<path d="M189.759 108.915L188.751 106.523C189.35 104.655 189.402 102.583 188.763 100.542C187.254 95.7285 182.235 92.8001 177.3 93.8437C171.597 95.05 168.153 100.893 169.913 106.495C171.407 111.251 176.325 114.173 181.218 113.218C183.646 112.744 185.673 111.431 187.081 109.652L187.079 109.657L189.759 108.915Z" fill="white"/>
<path d="M189.759 108.916C189.763 108.918 189.707 108.935 189.596 108.97C189.471 109.007 189.304 109.057 189.09 109.121C188.623 109.257 187.953 109.452 187.092 109.703C187.067 109.71 187.041 109.696 187.034 109.671C187.031 109.66 187.032 109.648 187.036 109.639L187.038 109.634L187.118 109.682C186.361 110.65 185.153 111.831 183.349 112.631C182.453 113.031 181.419 113.319 180.288 113.441C179.158 113.553 177.931 113.469 176.702 113.134C175.475 112.793 174.251 112.195 173.154 111.334C172.063 110.475 171.082 109.358 170.42 108.013C169.747 106.678 169.364 105.145 169.345 103.565C169.316 101.983 169.717 100.365 170.477 98.9058C171.24 97.4263 172.36 96.1654 173.688 95.2761C175.008 94.3729 176.509 93.8283 178.003 93.618C179.501 93.3992 180.991 93.5862 182.327 94.0088C183.666 94.4364 184.857 95.1228 185.832 95.9556C186.805 96.7929 187.565 97.7695 188.107 98.7744C188.23 99.0318 188.351 99.2848 188.47 99.5338C188.584 99.7852 188.662 100.046 188.758 100.294C188.94 100.795 189.054 101.296 189.141 101.779C189.48 103.726 189.173 105.384 188.795 106.538L188.794 106.506C189.107 107.278 189.351 107.881 189.522 108.305C189.599 108.498 189.659 108.65 189.705 108.764C189.744 108.866 189.763 108.917 189.759 108.916C189.756 108.914 189.729 108.859 189.682 108.754C189.631 108.64 189.562 108.487 189.476 108.293C189.295 107.881 189.038 107.294 188.709 106.541C188.704 106.531 188.704 106.519 188.707 106.509C189.062 105.359 189.345 103.719 188.994 101.804C188.903 101.329 188.787 100.836 188.604 100.345C188.509 100.102 188.43 99.8457 188.317 99.5997C188.198 99.3557 188.077 99.1077 187.954 98.8557C187.415 97.8722 186.665 96.9184 185.706 96.1014C184.746 95.2895 183.575 94.6219 182.263 94.2082C180.953 93.799 179.496 93.6205 178.032 93.8377C176.572 94.047 175.106 94.5822 173.818 95.4651C172.522 96.3346 171.429 97.5666 170.683 99.0125C169.941 100.437 169.549 102.018 169.575 103.562C169.592 105.105 169.964 106.605 170.618 107.912C171.262 109.228 172.218 110.322 173.284 111.167C174.354 112.014 175.552 112.606 176.753 112.945C177.957 113.279 179.16 113.369 180.271 113.266C181.382 113.153 182.402 112.879 183.287 112.492C185.07 111.72 186.277 110.569 187.043 109.623C187.059 109.602 187.089 109.599 187.109 109.614C187.127 109.628 187.132 109.652 187.124 109.671L187.122 109.676L187.066 109.611C187.921 109.387 188.587 109.213 189.051 109.091C189.272 109.035 189.444 108.991 189.573 108.957C189.693 108.928 189.755 108.914 189.759 108.916Z" fill="#263238"/>
<path d="M175.959 104.86C176.556 104.86 177.039 104.376 177.039 103.78C177.039 103.183 176.556 102.699 175.959 102.699C175.362 102.699 174.878 103.183 174.878 103.78C174.878 104.376 175.362 104.86 175.959 104.86Z" fill="#263238"/>
<path d="M180.682 103.78C180.682 104.377 180.198 104.86 179.601 104.86C179.004 104.86 178.52 104.376 178.52 103.78C178.52 103.182 179.004 102.699 179.601 102.699C180.198 102.699 180.682 103.183 180.682 103.78Z" fill="#263238"/>
<path d="M183.145 104.86C183.742 104.86 184.226 104.376 184.226 103.78C184.226 103.183 183.742 102.699 183.145 102.699C182.548 102.699 182.064 103.183 182.064 103.78C182.064 104.376 182.548 104.86 183.145 104.86Z" fill="#263238"/>
<path d="M58.4868 88.5291L58.957 85.7699C58.1247 84.3122 57.6718 82.6138 57.7319 80.8129C57.9045 75.6341 62.2425 71.5759 67.4212 71.7485C72.6 71.9211 76.6582 76.2591 76.4856 81.4378C76.313 86.6166 71.975 90.6748 66.7963 90.5022C64.6337 90.4303 62.6685 89.6298 61.123 88.3476L61.126 88.3521L58.4868 88.5291Z" fill="white"/>
<path d="M58.487 88.529C58.4845 88.5315 58.5396 88.53 58.6517 88.5251C58.7737 88.5191 58.9398 88.5107 59.1536 88.5003C59.6129 88.475 60.2756 88.4383 61.1297 88.3907C61.151 88.3897 61.1679 88.3708 61.1664 88.3495C61.1659 88.3421 61.1629 88.3346 61.1589 88.3287L61.156 88.3242L61.0989 88.3773C61.9967 89.1312 63.3622 89.9868 65.2033 90.3767C65.6616 90.4714 66.1462 90.5364 66.6512 90.5622C66.9036 90.579 67.1615 90.5607 67.4234 90.5617C67.5544 90.5572 67.6868 90.5636 67.8197 90.5503C67.9522 90.5349 68.0856 90.5195 68.22 90.5041C69.2948 90.3831 70.4242 90.0657 71.5015 89.5017C72.5779 88.9402 73.6001 88.1337 74.4413 87.104C75.283 86.0768 75.9402 84.8249 76.2949 83.4411C76.654 82.0592 76.6753 80.5474 76.3539 79.0758C76.2111 78.3338 75.9249 77.6205 75.6049 76.9207C75.2424 76.2421 74.8396 75.5686 74.3144 74.9823C73.8208 74.3782 73.2281 73.8747 72.6195 73.4149C71.9861 72.9894 71.3339 72.5951 70.6311 72.3356C69.2437 71.7811 67.7602 71.5589 66.3491 71.6854C64.937 71.8029 63.6017 72.2369 62.456 72.8892C61.3073 73.5404 60.3465 74.4059 59.6134 75.3642C58.1368 77.2932 57.6056 79.5341 57.6691 81.3906C57.7281 83.2675 58.3283 84.7679 58.923 85.7906L58.918 85.7639C58.7757 86.6601 58.6661 87.3535 58.5897 87.8332C58.5554 88.0574 58.5292 88.231 58.5098 88.3579C58.492 88.4735 58.4845 88.5315 58.487 88.529C58.487 88.529 58.5024 88.4641 58.5257 88.3435C58.55 88.2151 58.5832 88.039 58.6259 87.8118C58.7117 87.3401 58.8362 86.6581 58.9969 85.7767C58.9989 85.7673 58.9964 85.7574 58.9919 85.75C58.4166 84.7272 57.8387 83.2387 57.7946 81.3867C57.7455 79.5539 58.2821 77.3522 59.7409 75.4629C60.4655 74.524 61.4124 73.6778 62.5413 73.0419C63.6682 72.4051 64.9776 71.9825 66.3625 71.8704C67.7463 71.7484 69.2001 71.9691 70.5581 72.5132C71.2466 72.7672 71.885 73.155 72.5054 73.5717C73.1011 74.0225 73.6825 74.5156 74.1656 75.1078C74.6804 75.6822 75.0747 76.3423 75.4304 77.0065C75.7433 77.692 76.0251 78.3903 76.1654 79.117C76.4814 80.5578 76.463 82.0404 76.1138 83.3955C75.7691 84.7525 75.1273 85.9816 74.3044 86.9924C73.4821 88.0058 72.4811 88.8019 71.4252 89.3579C70.3687 89.9169 69.2601 90.2348 68.2021 90.3598C68.0697 90.3762 67.9383 90.392 67.8078 90.4079C67.6774 90.4218 67.5464 90.4163 67.418 90.4213C67.1601 90.4218 66.9056 90.4421 66.6566 90.4267C66.1581 90.4044 65.68 90.3439 65.2271 90.2541C63.4083 89.8836 62.0493 89.0518 61.148 88.3173C61.1312 88.3034 61.1069 88.3054 61.0925 88.3222C61.0806 88.3361 61.0806 88.3564 61.0905 88.3703L61.0935 88.3743L61.1227 88.3123C60.281 88.3793 59.6283 88.4308 59.1754 88.467C58.9577 88.4854 58.7886 88.4998 58.6646 88.5102C58.5505 88.5206 58.487 88.529 58.487 88.529Z" fill="#263238"/>
<path d="M63.0537 81.7701L64.0894 80.7344L66.0868 82.5101L70.0071 78.7375L70.9689 79.7732L65.9389 84.507L63.0537 81.7701Z" fill="#5F9EA0"/>
<path d="M229.364 189.927C229.376 189.81 229.387 189.694 229.392 189.578C229.492 187.346 228.585 185.207 227.713 183.15C225.373 177.634 223.11 171.781 223.767 165.828C224.637 157.956 230.425 151.446 232.492 143.832C232.735 142.936 233.558 142.319 234.488 142.319C235.886 142.319 236.9 143.673 236.472 145C234.17 152.12 228.919 158.361 228.095 165.828C227.438 171.781 229.701 177.634 232.04 183.15C232.913 185.207 233.819 187.346 233.72 189.578C233.621 191.81 232.185 194.156 229.973 194.513H229.759H225.646H197.078C195.932 194.513 195.003 193.587 195.003 192.445V191.996C195.003 190.853 195.932 189.927 197.078 189.927H229.364Z" fill="#455A64"/>
<path d="M235.978 239.242H188.244C187.577 239.242 186.957 238.914 186.542 238.342C186.066 237.687 185.936 236.825 186.193 236.036L199.781 194.398L200.49 194.629L186.902 236.268C186.718 236.83 186.809 237.442 187.145 237.904C187.418 238.28 187.818 238.496 188.244 238.496H235.978C236.397 238.496 236.781 238.296 237.057 237.932C237.391 237.491 237.497 236.896 237.343 236.341L225.665 194.614L226.383 194.413L238.06 236.141C238.278 236.919 238.124 237.758 237.65 238.383C237.237 238.929 236.627 239.242 235.978 239.242Z" fill="#E0E0E0"/>
<path d="M169.028 163.663C169.028 163.663 170.767 159.439 172.484 158.545C174.201 157.651 176.388 157.08 176.388 157.08H184.033L184.109 162.03C184.109 162.03 183.005 163.137 181.492 162.927C179.979 162.718 176.2 161.784 176.2 161.784L174.336 161.886L172.617 164.091C172.617 164.091 171.222 164.515 171.713 163.194C172.204 161.874 172.423 161.292 172.423 161.292C172.423 161.292 170.772 163.861 170.139 163.979C169.505 164.097 169.028 163.663 169.028 163.663Z" fill="#6F4439"/>
<path d="M200.627 147.731L194.543 156.482L184.034 157.081L183.891 161.881L191.809 163.44C195.922 164.25 200.17 162.989 203.174 160.066L204.333 149.482L200.627 147.731Z" fill="#AE7461"/>
<path d="M195.907 220.31L202.979 227.225C202.979 227.225 197.873 237.082 199.048 238.493L213.81 224.603L203.262 213.354L195.907 220.31Z" fill="#263238"/>
<path d="M206.971 222.327C207.327 222.132 207.811 222.249 208.003 222.584C208.195 222.919 208.052 223.416 207.7 223.617C207.348 223.819 206.76 223.696 206.609 223.34C206.457 222.984 206.656 222.407 207.043 222.293" fill="#E0E0E0"/>
<path d="M213.81 224.603L212.7 223.513L198.962 237.239C198.962 237.239 198.56 238.127 199.048 238.494L213.81 224.603Z" fill="#455A64"/>
<path d="M202.673 227.38C202.74 227.445 203.13 227.137 203.691 227.022C204.251 226.892 204.713 227.015 204.754 226.926C204.808 226.85 204.315 226.545 203.636 226.696C202.957 226.84 202.598 227.332 202.673 227.38Z" fill="white"/>
<path d="M201.697 229.441C201.743 229.522 202.164 229.344 202.682 229.401C203.202 229.443 203.558 229.696 203.627 229.625C203.705 229.572 203.378 229.127 202.732 229.068C202.088 229.004 201.637 229.374 201.697 229.441Z" fill="white"/>
<path d="M202.474 232.016C202.553 231.969 202.331 231.535 201.793 231.337C201.256 231.134 200.777 231.308 200.801 231.393C200.813 231.487 201.221 231.481 201.66 231.651C202.104 231.81 202.396 232.077 202.474 232.016Z" fill="white"/>
<path d="M201.247 225.616C201.336 225.65 201.578 225.244 202.01 224.887C202.435 224.522 202.874 224.352 202.857 224.258C202.852 224.17 202.304 224.21 201.8 224.637C201.294 225.061 201.161 225.596 201.247 225.616Z" fill="white"/>
<path d="M196.739 171.02C195.446 171.02 163.946 180.196 160.538 183.298C157.129 186.399 154.579 224.783 154.579 224.783L167.787 229.311C167.787 229.311 170.571 210.566 172.258 204.511C173.599 199.698 173.291 195.204 173.291 195.204L199.443 183.815L196.739 171.02Z" fill="#263238"/>
<path d="M197.533 173.05C196.945 173.308 174.059 183.08 171.908 189.626C170.155 194.961 172.193 199.659 174.641 203.33C177.406 207.474 197.677 225.972 197.677 225.972L209.117 217.791L189.502 197.535C189.502 197.535 199.794 191.181 200.147 191.181C200.499 191.181 211.198 190.474 214.589 190.09C217.756 189.732 219.488 185.191 219.488 185.191L222.313 172.171L201.087 167.273L197.533 173.05Z" fill="#263238"/>
<path d="M197.533 173.05C197.533 173.05 197.607 172.919 197.757 172.669C197.91 172.417 198.134 172.047 198.43 171.559C199.027 170.581 199.909 169.136 201.058 167.254L201.071 167.232L201.096 167.238C205.83 168.323 213.123 169.994 222.329 172.104L222.395 172.119L222.381 172.185C221.535 176.099 220.585 180.496 219.567 185.207L219.566 185.214L219.564 185.219C219.08 186.384 218.426 187.505 217.548 188.483C217.109 188.969 216.59 189.4 215.996 189.718C215.85 189.801 215.7 189.879 215.537 189.929C215.376 189.981 215.223 190.063 215.053 190.084C214.885 190.116 214.717 190.155 214.55 190.179L214.048 190.232C211.369 190.497 208.597 190.695 205.747 190.903C204.321 191 202.876 191.099 201.412 191.198C201.046 191.221 200.678 191.243 200.309 191.265L200.167 191.271C200.136 191.27 200.14 191.281 200.126 191.282C200.118 191.283 200.09 191.298 200.072 191.306C199.994 191.34 199.911 191.386 199.83 191.427L199.346 191.695C198.702 192.061 198.059 192.442 197.414 192.826C194.837 194.367 192.217 195.973 189.553 197.615L189.571 197.468C194.456 202.512 199.602 207.826 204.889 213.284C206.333 214.776 207.767 216.257 209.187 217.723L209.266 217.804L209.175 217.87C205.272 220.659 201.451 223.391 197.732 226.049L197.67 226.093L197.613 226.041C193.517 222.287 189.583 218.613 185.848 215.042C183.981 213.257 182.165 211.496 180.419 209.749C178.673 208.003 176.978 206.287 175.45 204.5C175.251 204.285 175.086 204.039 174.904 203.809C174.727 203.576 174.545 203.347 174.395 203.098C174.079 202.613 173.777 202.123 173.499 201.624C172.938 200.629 172.455 199.607 172.078 198.564C171.701 197.521 171.427 196.457 171.289 195.391C171.154 194.325 171.151 193.262 171.267 192.232C171.379 191.2 171.617 190.205 171.98 189.271C172.382 188.344 173.019 187.579 173.669 186.882C175.003 185.507 176.465 184.398 177.89 183.405C180.754 181.43 183.488 179.899 185.899 178.607C188.313 177.319 190.421 176.289 192.147 175.469C193.873 174.65 195.221 174.047 196.135 173.644C196.591 173.447 196.939 173.297 197.176 173.194C197.412 173.096 197.533 173.05 197.533 173.05C197.533 173.05 197.416 173.107 197.183 173.211C196.948 173.318 196.602 173.473 196.149 173.677C195.239 174.089 193.897 174.703 192.176 175.531C190.454 176.359 188.352 177.398 185.944 178.693C183.54 179.992 180.812 181.53 177.961 183.507C176.541 184.5 175.086 185.611 173.767 186.976C173.125 187.668 172.498 188.427 172.11 189.327C171.758 190.239 171.52 191.233 171.413 192.25C171.3 193.269 171.304 194.319 171.44 195.373C171.578 196.427 171.851 197.48 172.225 198.512C172.601 199.546 173.082 200.558 173.64 201.546C173.917 202.042 174.217 202.529 174.533 203.011C174.683 203.259 174.862 203.484 175.037 203.713C175.217 203.94 175.378 204.181 175.578 204.395C177.1 206.171 178.796 207.885 180.542 209.628C182.289 211.372 184.106 213.13 185.974 214.913C189.709 218.479 193.645 222.151 197.742 225.903L197.623 225.896C201.34 223.237 205.161 220.504 209.062 217.714L209.049 217.86C207.629 216.393 206.195 214.913 204.75 213.421C199.465 207.961 194.32 202.646 189.436 197.601L189.354 197.516L189.454 197.455C192.12 195.814 194.74 194.21 197.321 192.67C197.966 192.285 198.609 191.905 199.256 191.538L199.745 191.268C199.829 191.225 199.908 191.181 199.999 191.141C200.024 191.13 200.036 191.121 200.072 191.11C200.103 191.101 200.15 191.089 200.166 191.091L200.301 191.085C200.669 191.063 201.035 191.041 201.401 191.019C202.864 190.92 204.309 190.823 205.734 190.727C208.584 190.522 211.357 190.326 214.031 190.065C214.707 190.022 215.345 189.886 215.919 189.572C216.491 189.266 216.996 188.848 217.428 188.373C218.289 187.418 218.94 186.303 219.415 185.163L219.412 185.174C220.438 180.465 221.395 176.07 222.248 172.157L222.299 172.238C213.099 170.101 205.811 168.407 201.081 167.308L201.118 167.292C199.955 169.168 199.061 170.609 198.456 171.585C198.155 172.068 197.926 172.434 197.77 172.684C197.615 172.928 197.533 173.05 197.533 173.05Z" fill="#455A64"/>
<path d="M199.616 110.445C198.585 110.655 197.451 109.984 197.138 108.98C196.826 107.975 197.38 106.779 198.347 106.367C197.642 105.761 197.385 104.686 197.739 103.826C198.092 102.965 199.031 102.383 199.959 102.447C199.917 101.53 200.387 100.605 201.153 100.098C201.919 99.5904 202.954 99.519 203.783 99.9158C204.718 98.9605 206.344 98.7859 207.461 99.52C208.336 99.278 209.347 99.6668 209.834 100.432C210.321 101.197 210.245 102.278 209.656 102.967" fill="#263238"/>
<path d="M208.107 101.59L207.637 101.632C202.909 102.05 199.296 106.027 199.332 110.773C199.367 115.187 199.461 120.062 199.697 122.411C200.188 127.274 205.124 127.6 205.124 127.6L205.23 133.485L217.012 132.075L216.182 110.77C216.171 105.677 213.197 101.439 208.107 101.59Z" fill="#AE7461"/>
<path d="M205.117 127.6C205.117 127.6 208.208 127.509 211.296 125.166C211.296 125.166 210.043 128.89 205.146 128.881L205.117 127.6Z" fill="#6F4439"/>
<path d="M201.089 115.287C201.108 115.655 201.436 115.943 201.821 115.929C202.206 115.916 202.502 115.607 202.483 115.239C202.463 114.871 202.135 114.584 201.751 114.597C201.366 114.61 201.069 114.919 201.089 115.287Z" fill="#263238"/>
<path d="M200.589 113.062C200.684 113.145 201.176 112.709 201.924 112.644C202.671 112.569 203.254 112.906 203.327 112.807C203.363 112.763 203.261 112.606 203.006 112.454C202.756 112.301 202.339 112.168 201.874 112.211C201.408 112.255 201.031 112.461 200.821 112.657C200.606 112.854 200.543 113.025 200.589 113.062Z" fill="#263238"/>
<path d="M207.815 114.899C207.835 115.267 208.163 115.555 208.548 115.541C208.932 115.528 209.228 115.219 209.209 114.851C209.19 114.483 208.861 114.196 208.478 114.209C208.093 114.222 207.796 114.531 207.815 114.899Z" fill="#263238"/>
<path d="M207.955 112.551C208.049 112.634 208.542 112.198 209.289 112.134C210.036 112.059 210.619 112.396 210.692 112.297C210.728 112.253 210.627 112.097 210.372 111.944C210.121 111.792 209.705 111.659 209.239 111.702C208.773 111.745 208.397 111.952 208.187 112.147C207.972 112.344 207.908 112.515 207.955 112.551Z" fill="#263238"/>
<path d="M205.731 118.339C205.731 118.296 205.258 118.257 204.496 118.227C204.303 118.224 204.119 118.202 204.076 118.075C204.018 117.941 204.08 117.728 204.153 117.497C204.292 117.019 204.437 116.518 204.589 115.993C205.191 113.853 205.608 112.099 205.521 112.076C205.435 112.053 204.878 113.769 204.276 115.909C204.132 116.436 203.995 116.939 203.864 117.42C203.811 117.643 203.708 117.9 203.831 118.178C203.895 118.316 204.045 118.408 204.17 118.429C204.296 118.456 204.404 118.449 204.499 118.446C205.261 118.42 205.732 118.382 205.731 118.339Z" fill="#263238"/>
<path d="M207.051 109.73C207.167 109.911 207.79 109.648 208.577 109.553C209.362 109.44 210.04 109.53 210.095 109.327C210.118 109.23 209.963 109.072 209.664 108.945C209.369 108.818 208.929 108.739 208.458 108.801C207.987 108.864 207.59 109.054 207.346 109.252C207.1 109.452 206.999 109.644 207.051 109.73Z" fill="#263238"/>
<path d="M200.281 109.98C200.453 110.136 201.055 109.905 201.797 109.831C202.537 109.736 203.178 109.824 203.307 109.634C203.363 109.542 203.249 109.38 202.96 109.244C202.675 109.108 202.211 109.017 201.702 109.075C201.193 109.133 200.764 109.326 200.521 109.523C200.274 109.721 200.203 109.904 200.281 109.98Z" fill="#263238"/>
<path d="M199.603 108.498C199.603 108.498 200.617 105.336 202.024 104.494C203.727 103.476 204.666 105.361 206.32 104.737C207.975 104.113 209.601 103.137 211.45 104.295C213.474 105.562 212.674 106.647 212.982 108.797C214.231 117.512 216.049 119.847 216.14 115.682C216.15 115.225 217.372 114.755 217.389 112.829C217.411 110.332 217.241 107.318 216.533 105.609C215.936 104.17 215.001 103.221 213.387 102.227C210.84 100.657 207.553 100.909 205.343 101.331C203.134 101.753 199.062 104.699 199.603 108.498Z" fill="#263238"/>
<path d="M215.818 115.578C215.782 114.824 216.377 114.173 217.132 114.178C218.047 114.184 219.103 114.61 219.209 116.492C219.397 119.83 216.007 119.33 215.997 119.234C215.989 119.169 215.884 116.975 215.818 115.578Z" fill="#AE7461"/>
<path d="M216.652 117.937C216.666 117.926 216.713 117.975 216.813 118.014C216.912 118.053 217.078 118.073 217.248 117.999C217.594 117.849 217.859 117.273 217.842 116.67C217.833 116.366 217.753 116.081 217.632 115.847C217.516 115.608 217.349 115.447 217.169 115.425C216.99 115.394 216.871 115.508 216.835 115.604C216.795 115.7 216.82 115.765 216.803 115.772C216.794 115.781 216.729 115.72 216.748 115.581C216.758 115.514 216.792 115.433 216.867 115.364C216.944 115.295 217.06 115.255 217.184 115.261C217.442 115.265 217.687 115.488 217.82 115.744C217.968 116.001 218.065 116.322 218.074 116.663C218.091 117.337 217.782 117.986 217.307 118.152C217.076 118.226 216.876 118.167 216.771 118.094C216.663 118.017 216.641 117.944 216.652 117.937Z" fill="#6F4439"/>
<path d="M215.184 109.135C214.777 109.469 214.348 109.813 213.831 109.911C213.315 110.009 212.693 109.762 212.56 109.254C212.5 109.026 212.532 108.752 212.364 108.586C212.164 108.389 211.834 108.485 211.558 108.54C210.599 108.728 209.375 107.759 209.045 106.839C208.831 107.231 208.301 107.39 207.905 107.183C207.51 106.975 207.362 106.85 207.04 106.541C206.718 106.232 206.282 105.969 205.847 106.07C205.604 106.127 205.387 106.294 205.138 106.294C204.697 106.294 204.438 105.801 204.334 105.373C204.229 104.945 204.133 104.443 203.742 104.24C203.38 104.052 202.947 104.218 202.549 104.313C202.153 104.408 201.624 104.359 201.487 103.975C201.415 103.773 201.484 103.548 201.583 103.358C202.029 102.496 203.127 102.034 204.055 102.317C204.283 101.189 205.236 100.242 206.366 100.02C207.496 99.7992 208.736 100.317 209.372 101.276C210.02 100.575 211.018 100.217 211.963 100.347C212.907 100.476 213.772 101.089 214.207 101.938C214.998 101.653 215.949 101.885 216.52 102.501C217.092 103.117 217.25 104.083 216.907 104.849C217.536 105.138 218.101 105.688 218.173 106.375C218.247 107.063 217.636 107.809 216.956 107.686C217.272 108.198 217.117 108.946 216.624 109.292C216.131 109.637 215.376 109.527 215.002 109.055" fill="#263238"/>
<path d="M203.894 103.318C203.891 103.318 203.893 103.338 203.9 103.379C203.908 103.418 203.915 103.478 203.936 103.555C203.981 103.705 204.062 103.928 204.235 104.175C204.408 104.418 204.667 104.692 205.043 104.896C205.411 105.103 205.904 105.242 206.44 105.165L206.377 105.096C206.299 105.39 206.357 105.729 206.525 106.009C206.686 106.295 206.92 106.54 207.167 106.778C207.292 106.891 207.405 107.027 207.573 107.127C207.737 107.222 207.927 107.269 208.118 107.276C208.497 107.291 208.911 107.137 209.155 106.798L209.027 106.773C209.171 107.427 209.605 108.056 210.239 108.407C210.575 108.608 210.98 108.713 211.364 108.668C211.748 108.631 212.097 108.486 212.42 108.337L212.327 108.305C212.543 108.703 212.849 109.032 213.206 109.263C213.556 109.494 214.007 109.608 214.393 109.476C215.164 109.241 215.627 108.541 215.678 107.917L215.605 107.959C216.014 108.137 216.434 108.12 216.764 108.026C217.099 107.933 217.353 107.762 217.53 107.6C217.708 107.434 217.807 107.272 217.866 107.16C217.918 107.045 217.939 106.982 217.935 106.981C217.917 106.968 217.847 107.243 217.492 107.558C217.315 107.709 217.066 107.867 216.743 107.948C216.425 108.032 216.026 108.038 215.648 107.866L215.583 107.836L215.576 107.908C215.521 108.491 215.073 109.145 214.357 109.355C214.007 109.47 213.611 109.368 213.279 109.15C212.945 108.931 212.653 108.614 212.452 108.24L212.421 108.181L212.36 108.209C212.041 108.354 211.705 108.491 211.349 108.525C210.994 108.564 210.631 108.471 210.311 108.281C209.712 107.949 209.304 107.36 209.165 106.741L209.131 106.586L209.038 106.717C208.832 107.007 208.463 107.152 208.122 107.139C207.951 107.134 207.782 107.093 207.639 107.011C207.5 106.933 207.384 106.799 207.256 106.684C207.01 106.452 206.782 106.216 206.63 105.95C206.471 105.688 206.418 105.391 206.484 105.125L206.504 105.043L206.421 105.056C205.917 105.135 205.443 105.012 205.083 104.819C204.717 104.63 204.46 104.37 204.284 104.14C203.938 103.661 203.917 103.308 203.894 103.318Z" fill="#455A64"/>
<path d="M205.794 120.735C205.802 120.609 206.636 120.707 207.314 120.065C207.993 119.424 208.007 118.524 208.124 118.532C208.177 118.526 208.262 118.749 208.227 119.126C208.195 119.495 208.012 120.018 207.598 120.411C207.183 120.803 206.676 120.931 206.331 120.919C205.978 120.909 205.783 120.791 205.794 120.735Z" fill="#263238"/>
<path d="M157.582 219.843L156.044 230.521C156.044 230.521 144.078 232.37 143.592 234.312L165.324 238.37L168.44 221.842L157.582 219.843Z" fill="#263238"/>
<path d="M162.927 230.883C163.328 231.071 163.536 231.573 163.365 231.957C163.194 232.342 162.664 232.536 162.26 232.355C161.856 232.174 161.586 231.578 161.802 231.216C162.018 230.853 162.654 230.657 163.003 230.924" fill="#E0E0E0"/>
<path d="M165.324 238.369L165.569 236.69L144.641 233.427C144.641 233.427 143.6 233.645 143.592 234.311L165.324 238.369Z" fill="#455A64"/>
<path d="M155.711 230.35C155.696 230.452 156.219 230.596 156.681 231.017C157.156 231.427 157.346 231.912 157.45 231.891C157.551 231.889 157.503 231.259 156.933 230.759C156.369 230.254 155.705 230.254 155.711 230.35Z" fill="white"/>
<path d="M153.268 230.817C153.225 230.91 153.654 231.167 153.937 231.659C154.234 232.144 154.241 232.621 154.348 232.636C154.445 232.671 154.626 232.095 154.262 231.489C153.904 230.88 153.287 230.721 153.268 230.817Z" fill="white"/>
<path d="M151.5 233.159C151.593 233.199 151.832 232.724 151.66 232.123C151.493 231.52 151.032 231.21 150.972 231.285C150.897 231.356 151.165 231.712 151.298 232.208C151.444 232.7 151.397 233.13 151.5 233.159Z" fill="white"/>
<path d="M156.347 227.959C156.374 228.06 156.887 228.011 157.479 228.162C158.075 228.302 158.507 228.579 158.578 228.503C158.653 228.442 158.265 227.985 157.565 227.817C156.865 227.644 156.309 227.871 156.347 227.959Z" fill="white"/>
<path d="M204.694 134.243L205.141 132.223L217.011 126.328L218.046 127.944C218.046 127.944 222.488 131.559 224.189 134.696C225.89 137.833 223.873 163.523 223.589 167.272C223.017 174.827 220.194 184.716 220.194 184.716C220.194 184.716 216.385 182.064 205.179 177.463C193.974 172.862 193.373 170.779 193.373 170.779L199.976 162.405L204.694 134.243Z" fill="#5F9EA0"/>
<path d="M204.874 134.949C204.807 135.322 199.442 148.205 199.442 148.205C199.442 148.205 202.23 149.76 202.619 149.838C203.007 149.916 204.874 134.949 204.874 134.949Z" fill="#5F9EA0"/>
<path d="M204.333 137.062C204.386 137.07 203.91 140.344 203.27 144.374C202.629 148.404 202.068 151.663 202.015 151.655C201.962 151.646 202.438 148.373 203.078 144.343C203.718 140.314 204.28 137.054 204.333 137.062Z" fill="#263238"/>
<path d="M166.677 162.836C166.677 162.836 169.874 158.119 171.78 157.29C173.685 156.462 176.069 156.014 176.069 156.014L184.109 157.089L183.966 161.889C183.966 161.889 182.701 162.992 181.096 162.655C179.492 162.317 175.516 161.035 175.516 161.035L173.512 161.005L171.507 163.236C171.507 163.236 169.982 163.586 170.606 162.209C171.231 160.832 171.509 160.226 171.509 160.226C171.509 160.226 169.55 162.853 168.863 162.932C168.176 163.012 166.677 162.836 166.677 162.836Z" fill="#AE7461"/>
<path d="M208.691 143.941L203.117 156.894L184.033 157.08L183.89 161.881C183.89 161.881 202.292 168.37 207.193 167.11C212.093 165.849 220.22 148.52 220.22 148.52L208.691 143.941Z" fill="#AE7461"/>
<path d="M213.204 133.013C212.682 133.486 210.958 135.511 209.118 139.914C207.277 144.318 204.694 149.463 204.694 149.463L217.851 154.707C217.851 154.707 220.85 147.807 222.242 144.737C223.302 142.399 224.044 137.801 223.078 135.576C222.112 133.351 217.786 128.858 213.204 133.013Z" fill="#5F9EA0"/>
<path d="M213.204 133.012C213.204 133.012 213.242 132.971 213.324 132.897C213.408 132.825 213.526 132.709 213.696 132.577C214.032 132.31 214.555 131.931 215.318 131.618C216.077 131.322 217.119 131.123 218.269 131.399C219.417 131.658 220.611 132.363 221.641 133.415C222.154 133.942 222.637 134.555 223.015 135.271C223.385 135.998 223.544 136.844 223.587 137.707C223.67 139.438 223.424 141.29 222.902 143.162C222.65 144.105 222.217 145.015 221.814 145.938C221.406 146.866 220.99 147.813 220.567 148.777C219.719 150.705 218.84 152.701 217.94 154.747L217.902 154.833L217.814 154.798C217.754 154.774 217.689 154.748 217.624 154.722C212.965 152.861 208.552 151.099 204.662 149.545L204.573 149.51L204.616 149.425C205.845 146.962 206.933 144.667 207.865 142.611C208.805 140.56 209.513 138.706 210.291 137.26C211.046 135.806 211.758 134.721 212.298 134.022C212.567 133.671 212.791 133.416 212.949 133.25C213.108 133.084 213.204 133.012 213.204 133.012C213.205 133.014 213.185 133.036 213.145 133.078C213.1 133.127 213.043 133.189 212.97 133.268C212.819 133.44 212.602 133.7 212.342 134.055C211.818 134.761 211.123 135.852 210.384 137.307C209.624 138.753 208.929 140.609 207.998 142.67C207.076 144.733 205.997 147.032 204.773 149.502L204.727 149.381C208.619 150.93 213.034 152.686 217.696 154.541C217.762 154.567 217.827 154.593 217.887 154.617L217.762 154.668C218.664 152.623 219.544 150.626 220.393 148.698C220.819 147.735 221.236 146.788 221.646 145.861C222.054 144.931 222.479 144.043 222.731 143.112C223.252 141.261 223.503 139.421 223.428 137.715C223.388 136.863 223.236 136.04 222.882 135.338C222.518 134.639 222.045 134.032 221.543 133.51C220.535 132.467 219.366 131.767 218.245 131.504C217.121 131.224 216.099 131.407 215.345 131.688C214.588 131.986 214.062 132.352 213.719 132.608C213.546 132.734 213.425 132.845 213.337 132.912C213.251 132.981 213.204 133.012 213.204 133.012Z" fill="#263238"/>
<path d="M175.112 158.438C175.117 158.5 174.124 158.412 173.078 158.953C172.022 159.474 171.503 160.326 171.456 160.285C171.438 160.274 171.536 160.048 171.785 159.739C172.032 159.43 172.447 159.055 172.99 158.779C173.535 158.505 174.083 158.394 174.478 158.378C174.874 158.361 175.115 158.416 175.112 158.438Z" fill="#6F4439"/>
<path d="M206.077 150.014C206.082 150.016 206.068 150.062 206.035 150.148C205.996 150.249 205.947 150.373 205.887 150.526C205.744 150.874 205.548 151.35 205.304 151.944C204.783 153.177 204.057 154.895 203.197 156.929L203.175 156.981L203.118 156.982C201.382 157.001 199.409 157.024 197.322 157.048C193.662 157.073 190.349 157.095 187.925 157.111C186.739 157.11 185.772 157.109 185.078 157.108C184.757 157.104 184.499 157.1 184.304 157.097C184.126 157.093 184.033 157.087 184.033 157.08C184.033 157.073 184.126 157.066 184.303 157.058C184.499 157.051 184.756 157.043 185.077 157.032C185.771 157.018 186.738 156.998 187.924 156.974C190.347 156.943 193.66 156.9 197.32 156.854C199.407 156.837 201.38 156.821 203.116 156.807L203.036 156.86C203.922 154.837 204.671 153.128 205.208 151.902C205.471 151.316 205.682 150.847 205.836 150.504C205.906 150.355 205.963 150.234 206.009 150.137C206.05 150.054 206.073 150.012 206.077 150.014Z" fill="#6F4439"/>
<path d="M37.2212 167.272H33.5479V239.148H37.2212V167.272Z" fill="#455A64"/>
<path d="M188.003 167.779H184.33V239.156H188.003V167.779Z" fill="#455A64"/>
<path d="M191.642 166.563H24.3701V170.901H191.642V166.563Z" fill="#455A64"/>
<path d="M190.033 170.9C190.033 170.954 170.513 170.998 146.44 170.998C122.359 170.998 102.844 170.954 102.844 170.9C102.844 170.847 122.359 170.803 146.44 170.803C170.514 170.804 190.033 170.847 190.033 170.9Z" fill="#263238"/>
<path d="M141.885 141.193C142.325 141.11 142.749 141.399 142.833 141.839L147.359 165.827H169.371C169.7 165.827 169.966 166.094 169.966 166.423C169.966 166.751 169.699 167.018 169.371 167.018H146.026V166.923L145.919 166.943L141.239 142.14C141.156 141.7 141.445 141.276 141.885 141.193Z" fill="#263238"/>
<path d="M31.5708 85.2211C33.6654 82.1385 36.6751 80.2026 40.3812 79.8102C42.4724 79.589 43.3776 79.9466 44.9221 80.2194C46.4666 80.4917 47.8996 81.1797 48.9129 82.3765C50.1167 83.7981 51.1657 85.4165 51.5323 87.2428C51.9088 89.1187 51.9291 90.9851 51.7054 92.5445C51.4331 94.4383 50.4128 97.3875 49.4312 99.0302L46.2519 99.7073C43.5893 100.036 40.8896 100.365 38.2335 99.988C35.5775 99.6106 32.9249 98.436 31.3282 96.2804C29.1101 93.2851 29.4762 88.3042 31.5708 85.2211Z" fill="#263238"/>
<path d="M35.871 84.8059L42.0888 81.6687L43.7901 81.7143C47.5805 81.3899 50.5665 85.3361 50.8651 88.4029C51.1964 91.8099 51.4672 95.9828 51.1493 98.5446C50.5104 103.695 46.2349 104.5 46.2349 104.5C46.2349 104.5 46.9516 110.507 47.1351 112.201L36.8139 109.046L34.321 86.6882C34.2709 85.7538 34.944 84.9359 35.871 84.8059Z" fill="#FFBF9D"/>
<path d="M43.4637 98.0373C43.4751 97.768 43.6021 97.519 43.7767 97.3404C44.1254 96.9833 44.6744 96.9025 45.1035 97.1455C45.5325 97.3885 45.8147 97.9396 45.786 98.4783C45.5871 98.8101 45.2587 99.0516 44.8773 99.1191C44.8183 99.1295 44.7583 99.1369 44.6977 99.1409C44.4056 99.1593 44.1006 99.0873 43.859 98.8924C43.6179 98.698 43.4498 98.3701 43.4637 98.0373Z" fill="#FF9A6C"/>
<path d="M49.8783 93.0042C49.9026 93.3509 49.6288 93.657 49.2672 93.6887C48.9066 93.7204 48.5937 93.466 48.5699 93.1198C48.5456 92.7731 48.8193 92.4665 49.1799 92.4348C49.5415 92.4031 49.854 92.658 49.8783 93.0042Z" fill="#263238"/>
<path d="M49.862 90.2108C49.7837 90.299 49.2733 89.9479 48.566 89.9737C47.8597 89.9895 47.3528 90.371 47.2734 90.2866C47.2342 90.2494 47.3121 90.0922 47.5323 89.9201C47.7491 89.749 48.123 89.5769 48.5635 89.5635C49.0039 89.5511 49.3799 89.7004 49.5986 89.8591C49.8218 90.0188 49.9012 90.1711 49.862 90.2108Z" fill="#263238"/>
<path d="M45.8325 96.131C45.828 96.0909 46.2645 96.0001 46.9738 95.885C47.1538 95.8602 47.3235 95.8186 47.3498 95.6941C47.388 95.5621 47.3056 95.3702 47.2109 95.1633C47.0264 94.7323 46.8329 94.2805 46.6301 93.8068C45.8221 91.8749 45.2304 90.2837 45.3087 90.2519C45.3871 90.2202 46.1053 91.7603 46.9133 93.6922C47.1082 94.1689 47.2947 94.6232 47.4718 95.0577C47.5472 95.2606 47.6727 95.4892 47.5898 95.763C47.5457 95.8994 47.4167 96.0021 47.3021 96.0373C47.1876 96.0765 47.0864 96.0819 46.9971 96.0904C46.2809 96.1529 45.8365 96.1712 45.8325 96.131Z" fill="#263238"/>
<path d="M46.2425 104.494C46.2425 104.494 42.8037 104.834 39.3193 102.898C39.3193 102.898 41.2681 106.387 46.3789 105.732L46.2425 104.494Z" fill="#FF9A6C"/>
<path d="M49.8645 88.0453C49.7514 88.2219 49.2063 88.1252 48.5754 88.1991C47.9425 88.2532 47.4262 88.4575 47.2818 88.3067C47.2174 88.2328 47.2809 88.0622 47.4941 87.8811C47.7044 87.7011 48.072 87.528 48.5065 87.4839C48.9405 87.4402 49.3343 87.5354 49.5749 87.6704C49.8189 87.8053 49.9131 87.96 49.8645 88.0453Z" fill="#263238"/>
<path d="M42.6205 93.5347C42.6329 93.8819 42.9375 94.1577 43.3 94.1508C43.6621 94.1443 43.9463 93.8586 43.9334 93.5114C43.9205 93.1642 43.6165 92.8884 43.2544 92.8949C42.8918 92.9013 42.6081 93.1875 42.6205 93.5347Z" fill="#263238"/>
<path d="M40.3164 90.7871C40.4037 90.8665 40.8744 90.4637 41.5802 90.4146C42.2845 90.3561 42.8281 90.682 42.8986 90.5897C42.9333 90.549 42.8395 90.4002 42.6025 90.2524C42.3688 90.1051 41.9785 89.9732 41.5395 90.0064C41.1001 90.0406 40.742 90.2286 40.5416 90.4096C40.3362 90.5917 40.2737 90.7514 40.3164 90.7871Z" fill="#263238"/>
<path d="M41.2466 88.294C41.3944 88.4467 41.9306 88.2503 42.5853 88.2067C43.2385 88.1427 43.8015 88.2483 43.92 88.0737C43.9716 87.9894 43.8754 87.8337 43.6244 87.6948C43.3774 87.5564 42.9712 87.4547 42.5213 87.4914C42.0719 87.5291 41.6895 87.6953 41.4708 87.8724C41.2481 88.0494 41.1811 88.2191 41.2466 88.294Z" fill="#263238"/>
<path d="M35.9601 95.1774C35.9462 94.796 35.4745 93.9116 35.0956 93.8655C34.0843 93.743 32.2734 93.8853 32.3438 96.4412C32.44 99.935 35.9279 99.1176 35.9314 99.0174C35.9333 98.9391 36.0082 96.4948 35.9601 95.1774Z" fill="#FFBF9D"/>
<path d="M34.9323 97.7681C34.9164 97.7572 34.8728 97.8122 34.7711 97.8623C34.6719 97.9109 34.5003 97.9476 34.3163 97.8842C33.9433 97.7587 33.6169 97.1813 33.5827 96.5524C33.5648 96.2345 33.6234 95.9299 33.729 95.676C33.8287 95.4176 33.9889 95.2345 34.1739 95.1963C34.3575 95.1487 34.4924 95.2564 34.5375 95.3531C34.5871 95.4498 34.5668 95.5202 34.5851 95.5257C34.595 95.5341 34.6585 95.4652 34.6268 95.3218C34.6104 95.2529 34.5673 95.1715 34.4829 95.1066C34.3971 95.0406 34.2731 95.0093 34.1442 95.0267C33.8753 95.053 33.6397 95.3074 33.5232 95.5857C33.3922 95.8659 33.3193 96.2082 33.3392 96.5653C33.3808 97.2686 33.7593 97.9174 34.2682 98.0498C34.5157 98.1069 34.7185 98.0275 34.8222 97.9417C34.9283 97.8524 34.9447 97.7736 34.9323 97.7681Z" fill="#FF9A6C"/>
<path d="M34.955 93.8886L35.9198 94.7258C41.4809 91.0678 47.1358 81.1106 45.6751 81.5179C45.6751 81.5179 42.9679 80.6766 40.1759 81.4122C37.3844 82.1478 32.1343 84.046 32.5147 87.3821C32.8952 90.7172 34.955 93.8886 34.955 93.8886Z" fill="#263238"/>
<path d="M33.3216 79.9219C34.6752 80.713 35.8815 83.0199 35.3279 84.4871C35.5779 86.0212 34.9862 87.6665 33.8161 88.6897C32.646 89.7129 30.9368 90.0805 29.4498 89.6281C28.4226 89.3156 27.5085 88.6257 26.9564 87.7051C26.3012 86.6134 26.1782 85.2604 26.4123 84.0089C26.7 82.4674 27.5457 80.9967 28.8596 80.1416C30.1735 79.2865 31.968 79.1303 33.3216 79.9219Z" fill="#263238"/>
<path d="M33.0149 93.8966C33.0229 93.912 33.2505 93.8162 33.6513 93.6213C34.0526 93.4269 34.6254 93.1298 35.3183 92.7335C36.6992 91.9418 38.5706 90.7331 40.3289 89.0288C42.0892 87.3251 43.3119 85.4318 43.9542 83.9667C44.2806 83.2351 44.4869 82.6195 44.6015 82.1875C44.717 81.7555 44.7577 81.5115 44.7423 81.508C44.6922 81.4931 44.4745 82.4618 43.7846 83.8878C43.107 85.3143 41.8809 87.1584 40.1444 88.8399C38.4094 90.5223 36.5732 91.7444 35.2231 92.5732C34.5461 92.9879 33.9886 93.3088 33.6032 93.531C33.2173 93.7522 33.006 93.8792 33.0149 93.8966Z" fill="#455A64"/>
<path d="M29.9665 91.2738C30.0147 91.2758 30.035 90.6231 30.2299 89.5973C30.4219 88.5726 30.837 87.1793 31.5924 85.7781C32.3538 84.3794 33.2942 83.2704 34.0476 82.5497C34.8006 81.8255 35.3357 81.452 35.308 81.4133C35.2975 81.3985 35.1567 81.4803 34.9092 81.643C34.6592 81.8012 34.3184 82.0631 33.9202 82.4128C33.1226 83.1087 32.1365 84.2237 31.3598 85.6522C30.588 87.0831 30.1927 88.5176 30.0454 89.5661C29.97 90.0904 29.9368 90.5189 29.9407 90.815C29.9403 91.1106 29.9482 91.2733 29.9665 91.2738Z" fill="#455A64"/>
<path d="M33.6768 99.3773C33.6768 100.1 34.2625 100.686 34.9852 100.686C35.7079 100.686 36.2937 100.1 36.2937 99.3773C36.2937 98.6546 35.7079 98.0688 34.9852 98.0688C34.2625 98.0688 33.6768 98.6546 33.6768 99.3773Z" fill="#263238"/>
<path d="M45.997 98.5918C45.9826 98.4683 45.1657 98.6107 44.4629 98.014C43.758 97.4188 43.6965 96.5319 43.5815 96.5458C43.5284 96.5433 43.457 96.768 43.512 97.137C43.5631 97.4996 43.7714 98.0055 44.2015 98.3711C44.6325 98.7347 45.1389 98.8344 45.4787 98.8031C45.8254 98.7733 46.0119 98.6469 45.997 98.5918Z" fill="#263238"/>
<path d="M27.3563 143.074C27.3563 143.074 23.4547 156.84 25.7725 164.313C27.1628 168.796 46.677 193.494 47.6169 194.556C48.2711 195.295 39.2732 218.626 37.6269 223.486L47.9725 228.501C47.9725 228.501 60.9186 200.973 61.7097 196.149C62.726 189.954 48.1625 163.451 48.6977 163.095L53.9324 152.177L49.588 140.932C49.588 140.932 31.2781 141.13 27.5393 141.429L27.3563 143.074Z" fill="#263238"/>
<path d="M49.9881 151.192C49.9881 151.192 75.2276 160.146 78.0731 161.662C81.1756 163.314 83.5187 169.111 80.9321 174.038C78.3454 178.966 55.6544 193.073 55.6544 193.073L49.9881 184.45L66.4092 171.516L48.6331 167.328L42.8438 160.467L49.9881 151.192Z" fill="#263238"/>
<path d="M61.7106 195.991C61.6753 195.988 61.8271 195.364 61.6982 194.237C61.5816 193.111 61.1436 191.52 60.4205 189.626C60.0609 188.677 59.6298 187.652 59.1755 186.545C58.9449 185.995 58.7266 185.41 58.499 184.818C58.2743 184.228 58.0079 183.628 57.7436 183.011C56.6672 180.551 55.4416 177.862 54.153 175.042C52.0708 170.516 50.1468 166.336 48.6033 162.982L48.5879 162.949L48.5963 162.914C49.253 160.194 49.8314 157.97 50.2698 156.431C50.491 155.662 50.678 155.065 50.8234 154.665C50.9017 154.466 50.9573 154.313 51.0128 154.216C51.0694 154.12 51.0996 154.073 51.1041 154.077C51.1081 154.08 51.0862 154.133 51.0411 154.231C50.9965 154.328 50.9498 154.485 50.8819 154.684C50.7559 155.089 50.5878 155.69 50.3839 156.462C49.9802 158.008 49.4321 160.239 48.7987 162.962L48.7917 162.894C50.3631 166.225 52.3114 170.4 54.3876 174.934C55.6762 177.756 56.8959 180.448 57.9618 182.917C58.2242 183.537 58.4861 184.136 58.7112 184.739C58.9369 185.336 59.1467 185.911 59.3739 186.465C59.8183 187.573 60.2409 188.605 60.5911 189.562C61.2954 191.472 61.7106 193.085 61.7979 194.227C61.8976 195.371 61.7106 195.991 61.7106 195.991Z" fill="#455A64"/>
<path opacity="0.3" d="M50.346 156.375C50.2964 156.556 50.346 163.006 50.346 163.006L53.5844 168.495L50.9982 167.885L48.6973 163.095L50.346 156.375Z" fill="black"/>
<path d="M64.6677 155.389C63.9391 154.674 61.8029 152.668 60.3015 151.262C59.7271 150.725 60.0976 149.763 60.8843 149.746L78.1689 149.393C78.4253 149.388 78.6713 149.495 78.8419 149.687L82.4548 153.745C82.9513 154.302 82.5689 155.186 81.8224 155.205L65.3031 155.638C65.066 155.644 64.8374 155.555 64.6677 155.389Z" fill="#455A64"/>
<path d="M59.775 152.283C59.775 152.283 61.7838 151.391 63.7921 151.192C65.8009 150.992 66.8053 151.837 67.8812 151.737C68.957 151.637 71.8983 151.924 71.8983 152.713C71.8983 153.502 67.8092 153.789 67.8092 153.789L71.3958 154.937C71.3958 154.937 75.4848 154.435 75.5568 154.937C75.6287 155.439 71.8983 155.941 70.1766 156.157C68.455 156.372 58.4834 156.515 57.049 156.157C55.6141 155.798 45.4277 149.629 45.4277 149.629L50.3059 144.607L59.775 152.283Z" fill="#FFBF9D"/>
<path d="M34.6545 107.381C34.6545 107.381 29.9628 108.098 28.3582 113.206C27.024 117.454 29.569 134.908 30.6508 138.06C31.7325 141.211 43.3806 151.353 44.4043 150.491C45.4281 149.628 51.1043 144.607 51.1043 144.607L41.9913 133.837C41.9913 133.837 42.4739 121.048 41.8708 118.635C41.2676 116.223 37.188 107.139 34.6545 107.381Z" fill="#5F9EA0"/>
<path d="M28.9582 128.669C29.6248 134.731 28.3233 137.337 28.3233 137.337C27.2693 138.134 26.7406 139.454 26.627 140.443C26.565 140.982 26.9975 141.448 27.5397 141.429L49.5884 140.931C49.5884 140.931 52.674 141.225 52.6085 138.3C52.5351 135.034 53.4348 123.56 52.2494 119.05C51.8402 117.493 46.8053 109.904 46.8053 109.904L36.2946 103.944L35.1825 106.808C35.1825 106.808 31.8479 108.025 30.3247 109.988C29.1204 111.54 28.6973 112.611 28.2147 114.274C27.3026 117.415 28.6006 125.418 28.9582 128.669Z" fill="#5F9EA0"/>
<path d="M84.3946 118.552C84.3946 118.552 84.2021 118.38 83.5335 118.595C82.8649 118.811 80.9642 119.744 80.1022 119.966C79.2402 120.188 77.3712 120.908 77.3712 120.908C77.3712 120.908 76.3306 120.788 75.0093 121.702C73.6874 122.615 69.958 125.079 69.958 125.079L71.2392 128.706C71.2392 128.706 72.6801 128.465 73.0897 128.586C73.4994 128.708 74.7191 128.783 75.7329 128.478C76.7468 128.174 78.1192 127.625 78.1192 127.625C78.1192 127.625 79.4559 127.735 79.8066 127.25C80.1573 126.764 79.7803 125.889 79.7803 125.889C79.7803 125.889 81.0922 124.685 80.5283 124.009C79.9648 123.332 79.2635 122.583 79.2635 122.583C79.2635 122.583 83.1546 120.521 83.8897 119.974C84.6237 119.428 84.7686 118.99 84.3946 118.552Z" fill="#FFBF9D"/>
<path d="M69.9578 125.079L57.8564 129.152L52.9668 131.622L59.8304 135.322L71.2389 128.706L69.9578 125.079Z" fill="#FFBF9D"/>
<path d="M77.2879 122.088C77.3484 122.067 77.6153 123.507 77.1391 125.207C77.1203 125.272 77.1014 125.337 77.0836 125.398L76.9829 125.234C77.7854 125.378 78.5001 125.52 79.0145 125.635C79.5432 125.74 79.8225 125.963 79.7962 125.99C79.7768 126.017 79.5006 125.866 78.9822 125.809C78.4624 125.739 77.7403 125.628 76.9377 125.487L76.7969 125.463L76.837 125.323C76.8539 125.264 76.8723 125.201 76.8901 125.138C77.3519 123.498 77.2076 122.09 77.2879 122.088Z" fill="#FF9A6C"/>
<path d="M77.2299 123.81C77.2388 123.789 77.338 123.799 77.4794 123.891C77.6173 123.982 77.7968 124.167 77.8737 124.455L77.7438 124.365C77.9466 124.328 78.1807 124.303 78.4287 124.298C79.3166 124.279 79.9961 124.551 79.9718 124.614C79.9519 124.687 79.2744 124.537 78.4337 124.556C78.1971 124.561 77.9729 124.576 77.7765 124.596L77.6664 124.606L77.6465 124.505C77.5538 124.036 77.1952 123.854 77.2299 123.81Z" fill="#FF9A6C"/>
<path d="M79.0528 122.881C79.0945 122.943 78.7686 123.219 78.4755 123.639C78.1769 124.055 78.0251 124.453 77.9527 124.435C77.8858 124.424 77.9344 123.952 78.2647 123.489C78.5916 123.025 79.0201 122.822 79.0528 122.881Z" fill="#FF9A6C"/>
<path d="M46.5762 107.242L47.7914 110.507C49.1142 111.761 52.4081 115.286 53.0336 117.712C53.8515 120.884 57.5388 128.859 57.5388 128.859C57.5388 128.859 65.9003 125.294 66.4772 125.88C67.054 126.465 69.9834 133.336 69.0197 134.778C68.0559 136.22 59.5168 140 56.0627 139.151C52.6085 138.301 49.3329 133.301 49.3329 133.301L46.5762 107.242Z" fill="#5F9EA0"/>
<path d="M52.0661 117.744C52.0829 117.738 52.1791 118.003 52.3394 118.489C52.4247 118.73 52.5164 119.029 52.5908 119.386C52.6717 119.74 52.7793 120.14 52.8383 120.595C53.0045 121.496 53.0848 122.585 53.126 123.792C53.1458 125.001 53.1042 126.329 53.0174 127.717C52.8269 130.493 52.63 132.997 52.6523 134.815C52.6429 135.27 52.6702 135.68 52.6796 136.04C52.6856 136.401 52.7089 136.711 52.7307 136.964C52.7684 137.471 52.7808 137.753 52.7634 137.756C52.7461 137.758 52.6994 137.481 52.6305 136.974C52.5928 136.721 52.5551 136.411 52.5353 136.049C52.512 135.687 52.4718 135.274 52.4698 134.818C52.4009 132.99 52.5695 130.47 52.7595 127.701C52.8463 126.316 52.8949 124.996 52.8879 123.796C52.8602 122.598 52.7996 121.518 52.6578 120.623C52.4009 118.826 52.0095 117.761 52.0661 117.744Z" fill="#263238"/>
<path d="M57.4124 133.585C57.341 133.584 57.2988 132.499 57.3182 131.16C57.3375 129.822 57.4109 128.738 57.4824 128.739C57.5538 128.74 57.5959 129.826 57.5766 131.164C57.5572 132.502 57.4838 133.586 57.4124 133.585Z" fill="#263238"/>
<path d="M41.8108 117.176C41.8198 117.176 41.8262 117.277 41.8302 117.469C41.8327 117.689 41.8356 117.966 41.8396 118.308C41.85 119.071 41.8639 120.116 41.8813 121.392C41.912 123.995 42.0479 127.599 41.9408 131.574C41.9309 132.555 42.0182 133.531 42.4497 134.336C42.8817 135.139 43.53 135.789 44.0885 136.442C45.2179 137.732 46.2376 138.898 47.1027 139.886C47.9364 140.852 48.6194 141.644 49.1179 142.222C49.3367 142.484 49.5147 142.697 49.6561 142.866C49.7771 143.015 49.8371 143.096 49.8302 143.102C49.8232 143.108 49.7503 143.038 49.6164 142.9C49.4656 142.74 49.2756 142.537 49.0415 142.288C48.5277 141.723 47.8239 140.95 46.9643 140.006C46.0874 139.028 45.0527 137.874 43.9079 136.598C43.347 135.95 42.6907 135.309 42.225 134.455C41.7593 133.592 41.6715 132.565 41.6814 131.572C41.7885 127.598 41.6814 124.001 41.6973 121.394C41.6973 120.09 41.7126 119.035 41.7379 118.307C41.7488 117.942 41.7642 117.659 41.7771 117.468C41.7905 117.275 41.8019 117.175 41.8108 117.176Z" fill="#263238"/>
<path d="M34.2398 107.381C34.2507 107.41 33.7398 107.554 32.908 108.004C32.0877 108.456 30.9335 109.253 29.9316 110.615C29.4306 111.29 28.9738 112.102 28.6568 113.037C28.4897 113.502 28.3751 114.001 28.2913 114.519C28.2189 115.036 28.1767 115.585 28.1405 116.142C28.0725 117.258 28.0701 118.434 28.1464 119.648C28.2273 120.86 28.3954 122.118 28.5239 123.407C28.7962 125.985 29.0561 128.439 29.2922 130.676C29.5228 132.91 29.7039 134.927 30.1662 136.555C30.6101 138.187 31.296 139.397 31.8788 140.137C32.168 140.51 32.422 140.776 32.599 140.952C32.7776 141.125 32.8738 141.215 32.8674 141.223C32.8644 141.227 32.8371 141.208 32.7855 141.17C32.7349 141.13 32.6576 141.073 32.5633 140.99C32.3734 140.826 32.105 140.568 31.8 140.2C31.1854 139.47 30.4628 138.253 29.9901 136.605C29.4995 134.961 29.2987 132.93 29.0556 130.702C28.8131 128.465 28.5467 126.011 28.267 123.435C28.1385 122.145 27.9724 120.892 27.8935 119.664C27.8196 118.438 27.827 117.252 27.9024 116.127C27.9426 115.563 27.9877 115.019 28.0666 114.484C28.1564 113.952 28.2789 113.441 28.4545 112.967C28.7883 112.011 29.2649 111.187 29.7852 110.506C30.8263 109.132 32.0162 108.346 32.8609 107.915C33.287 107.7 33.6322 107.561 33.8723 107.487C34.1108 107.409 34.2373 107.372 34.2398 107.381Z" fill="#263238"/>
<path d="M46.7345 110.615C46.7241 110.63 46.5436 110.521 46.2241 110.311C45.864 110.066 45.4147 109.761 44.8795 109.398L45.0223 109.326C45.0159 110.013 45.0084 110.817 45.0005 111.702L44.999 111.89L44.8294 111.805C43.9753 111.375 43.0344 110.902 42.0443 110.405C40.1452 109.441 38.4325 108.557 37.1989 107.904C36.6216 107.593 36.1345 107.331 35.7437 107.12C35.4029 106.93 35.2179 106.817 35.2258 106.801C35.2338 106.785 35.4342 106.868 35.7888 107.03C36.1896 107.221 36.6885 107.459 37.2808 107.741C38.5356 108.351 40.2612 109.211 42.1609 110.174C43.1474 110.679 44.0849 111.159 44.936 111.594L44.7644 111.697C44.7932 110.813 44.8195 110.009 44.8418 109.323L44.8477 109.147L44.9851 109.251C45.5015 109.641 45.9345 109.968 46.2822 110.231C46.5837 110.466 46.7449 110.602 46.7345 110.615Z" fill="#263238"/>
<path d="M50.6418 220.877L46.5647 229.414C46.5647 229.414 54.3916 236.588 53.8008 238.242L35.9746 230.62L41.749 217.048L50.6418 220.877Z" fill="#263238"/>
<path d="M41.3671 226.327C40.9837 226.269 40.5869 226.534 40.5249 226.897C40.4629 227.261 40.7546 227.661 41.1375 227.725C41.5199 227.79 42.007 227.487 42.0263 227.117C42.0457 226.748 41.6767 226.295 41.2923 226.32" fill="#E0E0E0"/>
<path d="M35.9746 230.62L36.6135 229.276L53.4661 237.086C53.4661 237.086 54.1188 237.752 53.8008 238.242L35.9746 230.62Z" fill="#455A64"/>
<path d="M46.8903 229.452C46.8521 229.533 46.4003 229.384 45.8581 229.465C45.313 229.533 44.9381 229.795 44.8721 229.728C44.7987 229.678 45.1409 229.241 45.8001 229.154C46.4578 229.061 46.9419 229.384 46.8903 229.452Z" fill="white"/>
<path d="M48.4443 230.982C48.4295 231.07 47.9925 231.049 47.5466 231.271C47.0937 231.48 46.8561 231.824 46.7713 231.783C46.684 231.761 46.8318 231.254 47.3918 230.989C47.9493 230.718 48.4766 230.903 48.4443 230.982Z" fill="white"/>
<path d="M48.5934 233.551C48.5066 233.534 48.5632 233.072 48.9813 232.717C49.3969 232.359 49.884 232.356 49.891 232.441C49.9108 232.53 49.5423 232.658 49.204 232.955C48.8578 233.243 48.6832 233.579 48.5934 233.551Z" fill="white"/>
</svg>
`

export const launlftun =`<svg width="98" height="58" viewBox="0 0 98 58" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="97.5" height="58" rx="8" fill="#E5DADA"/>
<rect width="28" height="28" rx="4" transform="matrix(-1 0 0 1 33.5 17)" fill="#CFC8C9"/>
<rect width="6" height="6" rx="3" transform="matrix(-1 0 0 1 11.5 47)" fill="#958E8F"/>
</svg>
`
export const launlftse=`<svg width="98" height="58" viewBox="0 0 98 58" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.5 8C0.5 3.85787 3.85786 0.5 8 0.5H89.5C93.6421 0.5 97 3.85786 97 8V50C97 54.1421 93.6421 57.5 89.5 57.5H8C3.85786 57.5 0.5 54.1421 0.5 50V8Z" fill="#E5DADA"/>
<path d="M0.5 8C0.5 3.85787 3.85786 0.5 8 0.5H89.5C93.6421 0.5 97 3.85786 97 8V50C97 54.1421 93.6421 57.5 89.5 57.5H8C3.85786 57.5 0.5 54.1421 0.5 50V8Z" stroke="#5F9EA0"/>
<rect width="28" height="28" rx="4" transform="matrix(-1 0 0 1 33.5 17)" fill="#A1BCBD"/>
<rect width="6" height="6" rx="3" transform="matrix(-1 0 0 1 11.5 47)" fill="#5F9EA0"/>
</svg>
`

export const launrgtun=`<svg width="98" height="58" viewBox="0 0 98 58" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="0.5" width="97.5" height="58" rx="8" fill="#E5DADA"/>
<rect x="64.5" y="17" width="28" height="28" rx="4" fill="#CFC8C9"/>
<rect x="86.5" y="47" width="6" height="6" rx="3" fill="#958E8F"/>
</svg>
`

export const launrgtse=`<svg width="98" height="58" viewBox="0 0 98 58" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1" y="0.5" width="96.5" height="57" rx="7.5" fill="#E5DADA"/>
<rect x="1" y="0.5" width="96.5" height="57" rx="7.5" stroke="#5F9EA0"/>
<rect x="64.5" y="17" width="28" height="28" rx="4" fill="#A1BCBD"/>
<rect x="86.5" y="47" width="6" height="6" rx="3" fill="#5F9EA0"/>
</svg>
`

export const ai=`<svg width="20" height="22" viewBox="0 0 20 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.10613 3.448C7.70413 1.698 10.1221 1.645 10.8311 3.289L10.8911 3.449L11.6981 5.809C11.8831 6.35023 12.1819 6.84551 12.5746 7.26142C12.9672 7.67734 13.4444 8.00421 13.9741 8.22L14.1911 8.301L16.5511 9.107C18.3011 9.705 18.3541 12.123 16.7111 12.832L16.5511 12.892L14.1911 13.699C13.6497 13.8838 13.1542 14.1826 12.7381 14.5753C12.3221 14.9679 11.995 15.4452 11.7791 15.975L11.6981 16.191L10.8921 18.552C10.2941 20.302 7.87613 20.355 7.16813 18.712L7.10613 18.552L6.30013 16.192C6.11531 15.6506 5.8165 15.1551 5.42387 14.739C5.03124 14.3229 4.55392 13.9959 4.02413 13.78L3.80813 13.699L1.44813 12.893C-0.302872 12.295 -0.355872 9.877 1.28813 9.169L1.44813 9.107L3.80813 8.301C4.34936 8.11606 4.84464 7.81719 5.26055 7.42457C5.67646 7.03195 6.00334 6.55469 6.21913 6.025L6.30013 5.809L7.10613 3.448ZM16.9991 1.80688e-07C17.1862 -2.35972e-07 17.3695 0.0524783 17.5283 0.151472C17.687 0.250465 17.8148 0.392003 17.8971 0.56L17.9451 0.677L18.2951 1.703L19.3221 2.053C19.5096 2.1167 19.674 2.23462 19.7944 2.39182C19.9148 2.54902 19.9858 2.73842 19.9984 2.93602C20.011 3.13362 19.9647 3.33053 19.8653 3.50179C19.766 3.67304 19.618 3.81094 19.4401 3.898L19.3221 3.946L18.2961 4.296L17.9461 5.323C17.8823 5.51043 17.7643 5.6747 17.6071 5.79499C17.4498 5.91529 17.2604 5.98619 17.0628 5.99872C16.8652 6.01125 16.6683 5.96484 16.4971 5.86538C16.3259 5.76591 16.1881 5.61787 16.1011 5.44L16.0531 5.323L15.7031 4.297L14.6761 3.947C14.4886 3.8833 14.3243 3.76538 14.2039 3.60819C14.0835 3.45099 14.0125 3.26158 13.9999 3.06398C13.9872 2.86638 14.0335 2.66947 14.1329 2.49821C14.2323 2.32696 14.3803 2.18906 14.5581 2.102L14.6761 2.054L15.7021 1.704L16.0521 0.677C16.1196 0.479426 16.2471 0.307909 16.417 0.186499C16.5868 0.065089 16.7904 -0.000125281 16.9991 1.80688e-07Z" fill="url(#paint0_linear_19570_3722)"/>
<defs>
<linearGradient id="paint0_linear_19570_3722" x1="10.0471" y1="19.9056" x2="10.0471" y2="0" gradientUnits="userSpaceOnUse">
<stop stop-color="#04417F"/>
<stop offset="1" stop-color="#0776E5"/>
</linearGradient>
</defs>
</svg>
`

export const airobot = `<svg width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.40156 17.7977C11.2792 17.7614 13.0161 17.1432 14.0358 16.2137C14.9983 15.3337 15.2337 14.259 15.2711 13.4021H15.2766V12.6431C15.2763 12.1152 15.0664 11.6091 14.693 11.236C14.3197 10.8628 13.8134 10.6532 13.2856 10.6532H9.77656V10.6499H6.47656V10.6532H2.96756C1.86756 10.6532 0.976562 11.5442 0.976562 12.6442V13.4021H0.982063C1.01946 14.2601 1.25486 15.3348 2.21736 16.2126C3.23706 17.1432 4.97396 17.7614 7.85156 17.7977V17.7999H8.40156V17.7977Z" fill="url(#paint0_radial_20133_1367)"/>
<path d="M8.40156 17.7977C11.2792 17.7614 13.0161 17.1432 14.0358 16.2137C14.9983 15.3337 15.2337 14.259 15.2711 13.4021H15.2766V12.6431C15.2763 12.1152 15.0664 11.6091 14.693 11.236C14.3197 10.8628 13.8134 10.6532 13.2856 10.6532H9.77656V10.6499H6.47656V10.6532H2.96756C1.86756 10.6532 0.976562 11.5442 0.976562 12.6442V13.4021H0.982063C1.01946 14.2601 1.25486 15.3348 2.21736 16.2126C3.23706 17.1432 4.97396 17.7614 7.85156 17.7977V17.7999H8.40156V17.7977Z" fill="url(#paint1_linear_20133_1367)"/>
<path d="M8.40156 17.7977C11.2792 17.7614 13.0161 17.1432 14.0358 16.2137C14.9983 15.3337 15.2337 14.259 15.2711 13.4021H15.2766V12.6431C15.2763 12.1152 15.0664 11.6091 14.693 11.236C14.3197 10.8628 13.8134 10.6532 13.2856 10.6532H9.77656V10.6499H6.47656V10.6532H2.96756C1.86756 10.6532 0.976562 11.5442 0.976562 12.6442V13.4021H0.982063C1.01946 14.2601 1.25486 15.3348 2.21736 16.2126C3.23706 17.1432 4.97396 17.7614 7.85156 17.7977V17.7999H8.40156V17.7977Z" fill="url(#paint2_radial_20133_1367)"/>
<path d="M8.40156 17.7977C11.2792 17.7614 13.0161 17.1432 14.0358 16.2137C14.9983 15.3337 15.2337 14.259 15.2711 13.4021H15.2766V12.6431C15.2763 12.1152 15.0664 11.6091 14.693 11.236C14.3197 10.8628 13.8134 10.6532 13.2856 10.6532H9.77656V10.6499H6.47656V10.6532H2.96756C1.86756 10.6532 0.976562 11.5442 0.976562 12.6442V13.4021H0.982063C1.01946 14.2601 1.25486 15.3348 2.21736 16.2126C3.23706 17.1432 4.97396 17.7614 7.85156 17.7977V17.7999H8.40156V17.7977Z" fill="url(#paint3_radial_20133_1367)"/>
<path d="M8.40156 17.7977C11.2792 17.7614 13.0161 17.1432 14.0358 16.2137C14.9983 15.3337 15.2337 14.259 15.2711 13.4021H15.2766V12.6431C15.2763 12.1152 15.0664 11.6091 14.693 11.236C14.3197 10.8628 13.8134 10.6532 13.2856 10.6532H9.77656V10.6499H6.47656V10.6532H2.96756C1.86756 10.6532 0.976562 11.5442 0.976562 12.6442V13.4021H0.982063C1.01946 14.2601 1.25486 15.3348 2.21736 16.2126C3.23706 17.1432 4.97396 17.7614 7.85156 17.7977V17.7999H8.40156V17.7977Z" fill="url(#paint4_radial_20133_1367)"/>
<path d="M8.67813 0.749951C8.67813 0.604082 8.62018 0.464188 8.51703 0.361043C8.41389 0.257898 8.27399 0.199951 8.12813 0.199951C7.98226 0.199951 7.84236 0.257898 7.73922 0.361043C7.63607 0.464188 7.57812 0.604082 7.57812 0.749951V1.84995H8.67813V0.749951Z" fill="url(#paint5_linear_20133_1367)"/>
<path d="M2.625 2.95005C2.625 2.51244 2.79884 2.09276 3.10827 1.78332C3.41771 1.47389 3.83739 1.30005 4.275 1.30005H11.975C12.4126 1.30005 12.8323 1.47389 13.1417 1.78332C13.4512 2.09276 13.625 2.51244 13.625 2.95005V7.35005C13.625 7.78766 13.4512 8.20734 13.1417 8.51678C12.8323 8.82621 12.4126 9.00005 11.975 9.00005H4.275C3.83739 9.00005 3.41771 8.82621 3.10827 8.51678C2.79884 8.20734 2.625 7.78766 2.625 7.35005V2.95005Z" fill="url(#paint6_radial_20133_1367)"/>
<path d="M10.3266 4.05005C10.0348 4.05005 9.75504 4.16594 9.54875 4.37223C9.34246 4.57852 9.22656 4.85831 9.22656 5.15005C9.22656 5.44179 9.34246 5.72158 9.54875 5.92787C9.75504 6.13416 10.0348 6.25005 10.3266 6.25005C10.6183 6.25005 10.8981 6.13416 11.1044 5.92787C11.3107 5.72158 11.4266 5.44179 11.4266 5.15005C11.4266 4.85831 11.3107 4.57852 11.1044 4.37223C10.8981 4.16594 10.6183 4.05005 10.3266 4.05005Z" fill="url(#paint7_linear_20133_1367)"/>
<path d="M4.82812 5.15005C4.82812 4.85831 4.94402 4.57852 5.15031 4.37223C5.3566 4.16594 5.63639 4.05005 5.92813 4.05005C6.21986 4.05005 6.49965 4.16594 6.70594 4.37223C6.91223 4.57852 7.02813 4.85831 7.02813 5.15005C7.02813 5.44179 6.91223 5.72158 6.70594 5.92787C6.49965 6.13416 6.21986 6.25005 5.92813 6.25005C5.63639 6.25005 5.3566 6.13416 5.15031 5.92787C4.94402 5.72158 4.82812 5.44179 4.82812 5.15005Z" fill="url(#paint8_linear_20133_1367)"/>
<defs>
<radialGradient id="paint0_radial_20133_1367" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-2.32344 8.4499) rotate(30.4033) scale(19.888 31.6758)">
<stop stop-color="#F08AF4"/>
<stop offset="0.535" stop-color="#9C6CFE"/>
<stop offset="1" stop-color="#4E44DB"/>
</radialGradient>
<linearGradient id="paint1_linear_20133_1367" x1="8.12656" y1="9.7985" x2="11.3617" y2="21.8853" gradientUnits="userSpaceOnUse">
<stop stop-color="#885EDB" stop-opacity="0"/>
<stop offset="1" stop-color="#E362F8"/>
</linearGradient>
<radialGradient id="paint2_radial_20133_1367" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(13.0766 13.9499) rotate(-90) scale(3.3 1.87344)">
<stop stop-color="#4E44DB"/>
<stop offset="1" stop-color="#4E44DB" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint3_radial_20133_1367" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(12.5266 12.8499) rotate(-135) scale(3.11127 2.33345)">
<stop stop-color="#4E44DB"/>
<stop offset="1" stop-color="#4E44DB" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint4_radial_20133_1367" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(13.0766 12.2999) rotate(180) scale(4.95 1.94218)">
<stop stop-color="#4E44DB"/>
<stop offset="1" stop-color="#4E44DB" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint5_linear_20133_1367" x1="7.55283" y1="0.199951" x2="8.71333" y2="1.55185" gradientUnits="userSpaceOnUse">
<stop stop-color="#8B52F4"/>
<stop offset="1" stop-color="#3D35B1"/>
</linearGradient>
<radialGradient id="paint6_radial_20133_1367" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-1.2248 -1.99988) rotate(39.055) scale(18.6806 35.6865)">
<stop stop-color="#F08AF4"/>
<stop offset="0.535" stop-color="#9C6CFE"/>
<stop offset="1" stop-color="#4E44DB"/>
</radialGradient>
<linearGradient id="paint7_linear_20133_1367" x1="9.74796" y1="4.13475" x2="11.4156" y2="7.01235" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDFDFD"/>
<stop offset="1" stop-color="#F9DCFA"/>
</linearGradient>
<linearGradient id="paint8_linear_20133_1367" x1="5.34953" y1="4.13475" x2="7.01712" y2="7.01235" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDFDFD"/>
<stop offset="1" stop-color="#F9DCFA"/>
</linearGradient>
</defs>
</svg>
`;

export const micicon = `<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="30" height="30" rx="15" fill="#F2F5F5"/>
<path d="M19.1303 14.3387C19.2501 14.3388 19.3659 14.3822 19.4563 14.4609C19.5466 14.5397 19.6053 14.6486 19.6216 14.7673L19.6263 14.8348V15.1655C19.6264 16.2921 19.2005 17.3772 18.4339 18.203C17.6674 19.0287 16.617 19.5341 15.4934 19.6178L15.4928 21.1177C15.4927 21.2434 15.445 21.3644 15.3592 21.4562C15.2734 21.548 15.1559 21.6039 15.0305 21.6124C14.9052 21.621 14.7812 21.5817 14.6837 21.5024C14.5862 21.423 14.5225 21.3097 14.5053 21.1852L14.5007 21.1177V19.6178C13.4038 19.5363 12.3757 19.0528 11.6133 18.2599C10.851 17.4671 10.4082 16.4207 10.3698 15.3215L10.3672 15.1655V14.8348C10.3672 14.7091 10.415 14.5881 10.5008 14.4963C10.5866 14.4045 10.704 14.3486 10.8294 14.3401C10.9548 14.3315 11.0787 14.3708 11.1762 14.4501C11.2737 14.5295 11.3375 14.6428 11.3546 14.7673L11.3592 14.8348V15.1655C11.3592 16.0616 11.7056 16.9232 12.3261 17.5698C12.9466 18.2164 13.7931 18.5981 14.6885 18.635L14.8314 18.6376H15.1621C16.0583 18.6377 16.9198 18.2912 17.5664 17.6707C18.213 17.0502 18.5947 16.2037 18.6316 15.3083L18.6342 15.1655V14.8348C18.6342 14.7032 18.6865 14.5771 18.7795 14.484C18.8725 14.391 18.9987 14.3387 19.1303 14.3387ZM14.9967 8.38647C15.6984 8.38647 16.3712 8.66519 16.8674 9.16131C17.3635 9.65743 17.6422 10.3303 17.6422 11.0319V15.0001C17.6422 15.7017 17.3635 16.3746 16.8674 16.8707C16.3712 17.3668 15.6984 17.6456 14.9967 17.6456C14.2951 17.6456 13.6222 17.3668 13.1261 16.8707C12.63 16.3746 12.3513 15.7017 12.3513 15.0001V11.0319C12.3513 10.3303 12.63 9.65743 13.1261 9.16131C13.6222 8.66519 14.2951 8.38647 14.9967 8.38647Z" fill="#8C9191"/>
</svg>`;

export const micicon_hover = `<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="30" height="30" rx="15" fill="#DFDFDF"/>
<path d="M19.1303 14.3387C19.2501 14.3388 19.3659 14.3822 19.4563 14.4609C19.5466 14.5397 19.6053 14.6486 19.6216 14.7673L19.6263 14.8348V15.1655C19.6264 16.2921 19.2005 17.3772 18.4339 18.203C17.6674 19.0287 16.617 19.5341 15.4934 19.6178L15.4928 21.1177C15.4927 21.2434 15.445 21.3644 15.3592 21.4562C15.2734 21.548 15.1559 21.6039 15.0305 21.6124C14.9052 21.621 14.7812 21.5817 14.6837 21.5024C14.5862 21.423 14.5225 21.3097 14.5053 21.1852L14.5007 21.1177V19.6178C13.4038 19.5363 12.3757 19.0528 11.6133 18.2599C10.851 17.4671 10.4082 16.4207 10.3698 15.3215L10.3672 15.1655V14.8348C10.3672 14.7091 10.415 14.5881 10.5008 14.4963C10.5866 14.4045 10.704 14.3486 10.8294 14.3401C10.9548 14.3315 11.0787 14.3708 11.1762 14.4501C11.2737 14.5295 11.3375 14.6428 11.3546 14.7673L11.3592 14.8348V15.1655C11.3592 16.0616 11.7056 16.9232 12.3261 17.5698C12.9466 18.2164 13.7931 18.5981 14.6885 18.635L14.8314 18.6376H15.1621C16.0583 18.6377 16.9198 18.2912 17.5664 17.6707C18.213 17.0502 18.5947 16.2037 18.6316 15.3083L18.6342 15.1655V14.8348C18.6342 14.7032 18.6865 14.5771 18.7795 14.484C18.8725 14.391 18.9987 14.3387 19.1303 14.3387ZM14.9967 8.38647C15.6984 8.38647 16.3712 8.66519 16.8674 9.16131C17.3635 9.65743 17.6422 10.3303 17.6422 11.0319V15.0001C17.6422 15.7017 17.3635 16.3746 16.8674 16.8707C16.3712 17.3668 15.6984 17.6456 14.9967 17.6456C14.2951 17.6456 13.6222 17.3668 13.1261 16.8707C12.63 16.3746 12.3513 15.7017 12.3513 15.0001V11.0319C12.3513 10.3303 12.63 9.65743 13.1261 9.16131C13.6222 8.66519 14.2951 8.38647 14.9967 8.38647Z" fill="#8C9191"/>
</svg>`;

export const upload = `<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 15C0 6.71573 6.71573 0 15 0C23.2843 0 30 6.71573 30 15C30 23.2843 23.2843 30 15 30C6.71573 30 0 23.2843 0 15Z" fill="#F2F5F5"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.756 20.5112C11.7879 20.5112 10.8774 20.1342 10.1927 19.4495C8.7795 18.0363 8.7795 15.7363 10.1927 14.3231L15.027 9.48877L15.7816 10.244L10.9474 15.0776C9.95029 16.0753 9.95029 17.6978 10.9474 18.6949C11.4305 19.178 12.0731 19.4442 12.756 19.4442C13.4389 19.4442 14.0815 19.178 14.5646 18.6949L19.3473 13.9122C19.9418 13.3176 19.9418 12.3508 19.3473 11.7562C18.7711 11.1794 17.7663 11.1812 17.1913 11.7556L12.5076 16.4357C12.4092 16.5341 12.3552 16.6651 12.3552 16.8044C12.3552 16.9438 12.4092 17.0748 12.5082 17.1732C12.705 17.3718 13.0494 17.3706 13.2462 17.1732L17.1782 13.2417L17.9328 13.997L14.0014 17.9284C13.3808 18.5478 12.373 18.5467 11.7536 17.9284C11.1341 17.3089 11.1341 16.3 11.7536 15.6811L16.4372 11.0016C16.9257 10.5126 17.5771 10.2428 18.2695 10.2428C18.9613 10.2428 19.6122 10.512 20.1024 11.0016C21.1126 12.0123 21.1126 13.6567 20.1024 14.6674L15.3192 19.4495C14.6346 20.1342 13.7246 20.5112 12.756 20.5112Z" fill="#8C9191"/>
</svg>`;

export const upload_hover = `<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 15C0 6.71573 6.71573 0 15 0C23.2843 0 30 6.71573 30 15C30 23.2843 23.2843 30 15 30C6.71573 30 0 23.2843 0 15Z" fill="#DFDFDF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.756 20.5112C11.7879 20.5112 10.8774 20.1342 10.1927 19.4495C8.7795 18.0363 8.7795 15.7363 10.1927 14.3231L15.027 9.48877L15.7816 10.244L10.9474 15.0776C9.95029 16.0753 9.95029 17.6978 10.9474 18.6949C11.4305 19.178 12.0731 19.4442 12.756 19.4442C13.4389 19.4442 14.0815 19.178 14.5646 18.6949L19.3473 13.9122C19.9418 13.3176 19.9418 12.3508 19.3473 11.7562C18.7711 11.1794 17.7663 11.1812 17.1913 11.7556L12.5076 16.4357C12.4092 16.5341 12.3552 16.6651 12.3552 16.8044C12.3552 16.9438 12.4092 17.0748 12.5082 17.1732C12.705 17.3718 13.0494 17.3706 13.2462 17.1732L17.1782 13.2417L17.9328 13.997L14.0014 17.9284C13.3808 18.5478 12.373 18.5467 11.7536 17.9284C11.1341 17.3089 11.1341 16.3 11.7536 15.6811L16.4372 11.0016C16.9257 10.5126 17.5771 10.2428 18.2695 10.2428C18.9613 10.2428 19.6122 10.512 20.1024 11.0016C21.1126 12.0123 21.1126 13.6567 20.1024 14.6674L15.3192 19.4495C14.6346 20.1342 13.7246 20.5112 12.756 20.5112Z" fill="#8C9191"/>
</svg>`;

export const send = `<svg width="20" height="20" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M29.0413 14.5667L1.0335 0.524331C0.919664 0.467411 0.789084 0.454018 0.6652 0.484152C0.527707 0.518143 0.409285 0.60523 0.335862 0.726345C0.26244 0.84746 0.240001 0.992734 0.273459 1.13036L3.15962 12.9228C3.20315 13.1002 3.33373 13.2442 3.50783 13.3011L8.45315 14.9987L3.51118 16.6962C3.33707 16.7565 3.20649 16.8971 3.16632 17.0746L0.273459 28.8837C0.243325 29.0076 0.256718 29.1382 0.313638 29.2487C0.444218 29.5132 0.765647 29.6203 1.0335 29.4897L29.0413 15.5277C29.1451 15.4775 29.2288 15.3904 29.2824 15.29C29.413 15.0221 29.3058 14.7007 29.0413 14.5667ZM3.5748 25.5288L5.25895 18.6449L15.1429 15.2531C15.2199 15.2263 15.2835 15.1661 15.3103 15.0857C15.3572 14.9451 15.2835 14.7944 15.1429 14.7442L5.25895 11.3558L3.58149 4.49866L24.6083 15.0422L3.5748 25.5288Z" fill="#5F9EA0"/>
</svg>
`;

export const threedotmenu = `
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14 5C14 3.9 13.1 3 12 3C10.9 3 10 3.9 10 5C10 6.1 10.9 7 12 7C13.1 7 14 6.1 14 5Z" stroke="#5F9EA0" stroke-opacity="0.5" stroke-width="1.5"/>
<path d="M14 19C14 17.9 13.1 17 12 17C10.9 17 10 17.9 10 19C10 20.1 10.9 21 12 21C13.1 21 14 20.1 14 19Z" stroke="#5F9EA0" stroke-opacity="0.5" stroke-width="1.5"/>
<path d="M14 12C14 10.9 13.1 10 12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12Z" stroke="#5F9EA0" stroke-opacity="0.5" stroke-width="1.5"/>
</svg>`;

export const announcement_placeholder = `<svg width="520" height="347" viewBox="0 0 520 347" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_20148_1402)">
<rect x="16" y="12" width="488" height="315" rx="12" fill="white" shape-rendering="crispEdges"/>
<rect x="21" y="17" width="478" height="165" rx="8" fill="#D4D4D4"/>
<path d="M268.228 83.0039H251.731C249.158 83.0039 246.689 84.0263 244.87 85.8461C243.05 87.666 242.027 90.1342 242.027 92.7079V106.293C242.027 107.568 242.278 108.83 242.766 110.007C243.254 111.184 243.968 112.254 244.87 113.155C246.689 114.975 249.158 115.997 251.731 115.997H268.228C269.502 115.997 270.764 115.746 271.942 115.259C273.119 114.771 274.189 114.056 275.09 113.155C275.991 112.254 276.706 111.184 277.193 110.007C277.681 108.83 277.932 107.568 277.932 106.293V92.7079C277.932 91.4335 277.681 90.1717 277.193 88.9943C276.706 87.817 275.991 86.7472 275.09 85.8461C274.189 84.945 273.119 84.2302 271.942 83.7426C270.764 83.2549 269.502 83.0039 268.228 83.0039Z" stroke="#9E9E9E" stroke-width="2.91118" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M242.512 109.205L247.849 102.994C248.547 102.301 249.464 101.87 250.443 101.774C251.423 101.679 252.406 101.925 253.225 102.47C254.044 103.016 255.027 103.262 256.006 103.166C256.986 103.071 257.902 102.64 258.601 101.946L263.123 97.4243C264.422 96.1205 266.143 95.3209 267.977 95.168C269.811 95.0151 271.64 95.519 273.137 96.5897L277.97 100.335M252.216 95.9493C252.639 95.9493 253.058 95.8659 253.449 95.704C253.839 95.5421 254.195 95.3048 254.494 95.0057C254.793 94.7065 255.03 94.3513 255.192 93.9605C255.354 93.5696 255.437 93.1507 255.437 92.7276C255.437 92.3045 255.354 91.8856 255.192 91.4947C255.03 91.1038 254.793 90.7486 254.494 90.4495C254.195 90.1503 253.839 89.913 253.449 89.7511C253.058 89.5892 252.639 89.5059 252.216 89.5059C251.361 89.5059 250.542 89.8453 249.938 90.4495C249.333 91.0537 248.994 91.8731 248.994 92.7276C248.994 93.582 249.333 94.4015 249.938 95.0057C250.542 95.6099 251.361 95.9493 252.216 95.9493Z" stroke="#9E9E9E" stroke-width="2.91118" stroke-linecap="round" stroke-linejoin="round"/>
<rect x="97.5273" y="189.764" width="324.944" height="25.5075" rx="5.54511" fill="#D9D9D9"/>
<rect x="97.5273" y="228.579" width="324.944" height="25.5075" rx="5.54511" fill="#D9D9D9"/>
<rect x="147.434" y="267.396" width="225.132" height="36.5977" rx="5.54511" fill="#A9ABAE"/>
<path d="M268.517 82.793H252.02C249.447 82.793 246.978 83.8153 245.159 85.6352C243.339 87.455 242.316 89.9233 242.316 92.4969V106.082C242.316 107.357 242.567 108.619 243.055 109.796C243.543 110.973 244.258 112.043 245.159 112.944C246.978 114.764 249.447 115.786 252.02 115.786H268.517C269.791 115.786 271.053 115.535 272.231 115.048C273.408 114.56 274.478 113.845 275.379 112.944C276.28 112.043 276.995 110.973 277.482 109.796C277.97 108.619 278.221 107.357 278.221 106.082V92.4969C278.221 91.2226 277.97 89.9607 277.482 88.7834C276.995 87.606 276.28 86.5363 275.379 85.6352C274.478 84.7341 273.408 84.0193 272.231 83.5316C271.053 83.044 269.791 82.793 268.517 82.793Z" stroke="white" stroke-width="2.91118" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M242.801 108.994L248.138 102.783C248.836 102.09 249.753 101.659 250.733 101.563C251.712 101.468 252.695 101.714 253.514 102.259C254.333 102.805 255.316 103.051 256.295 102.956C257.275 102.86 258.192 102.429 258.89 101.735L263.412 97.2133C264.711 95.9096 266.432 95.1099 268.266 94.9571C270.1 94.8042 271.929 95.3081 273.426 96.3788L278.259 100.125M252.505 95.7383C252.928 95.7383 253.347 95.655 253.738 95.4931C254.129 95.3312 254.484 95.0939 254.783 94.7947C255.082 94.4956 255.319 94.1404 255.481 93.7495C255.643 93.3587 255.726 92.9397 255.726 92.5166C255.726 92.0936 255.643 91.6746 255.481 91.2837C255.319 90.8929 255.082 90.5377 254.783 90.2385C254.484 89.9394 254.129 89.7021 253.738 89.5402C253.347 89.3783 252.928 89.2949 252.505 89.2949C251.65 89.2949 250.831 89.6344 250.227 90.2385C249.622 90.8427 249.283 91.6622 249.283 92.5166C249.283 93.3711 249.622 94.1905 250.227 94.7947C250.831 95.3989 251.65 95.7383 252.505 95.7383Z" stroke="white" stroke-width="2.91118" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_d_20148_1402" x="0.473685" y="0.909775" width="519.053" height="346.053" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.43609"/>
<feGaussianBlur stdDeviation="7.76316"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_20148_1402"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_20148_1402" result="shape"/>
</filter>
</defs>
</svg>
`;
export const chkcloseicon = `
<svg width="12" height="12" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M1.4 14L0 12.6L5.6 7L0 1.4L1.4 0L7 5.6L12.6 0L14 1.4L8.4 7L14 12.6L12.6 14L7 8.4L1.4 14Z" 
        fill="#fff" 
        stroke="#fff" 
        stroke-width="1"/>
</svg>
`;

export const beta =`
<svg xmlns="http://www.w3.org/2000/svg" width="70" height="20" viewBox="0 0 80 30">
  <rect width="100%" height="100%" fill="#ebebeb" rx="16" ry="16"></rect>
  <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" , font-size="16" fill="#66798a" font-weight="600">
    BETA
  </text>
</svg>
`;

export const CustomCursor =`
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_22619_2107)">
<path d="M10.6279 10.6278C10.8243 10.4321 11.1182 10.371 11.3778 10.4686L27.5496 16.6964C27.8359 16.8069 28.0173 17.0899 27.9987 17.3961C27.9801 17.7017 27.7653 17.9605 27.4672 18.0347L19.9217 19.9216L18.0348 27.4672C17.9606 27.7651 17.7018 27.9801 17.3962 27.9986C17.0665 28.0143 16.8006 27.8198 16.6965 27.5496L10.4686 11.3777C10.3691 11.1181 10.4316 10.8242 10.6279 10.6278Z" fill="url(#paint0_linear_22619_2107)"/>
<path d="M8.302 9.29556L6.31277 7.30633C6.03813 7.03169 6.03813 6.5867 6.31277 6.31206C6.58741 6.03742 7.03239 6.03742 7.30703 6.31206L9.29627 8.3013C9.57091 8.57594 9.57091 9.02092 9.29627 9.29556C9.02158 9.57025 8.57664 9.57025 8.302 9.29556ZM11.7819 4C12.1706 4 12.485 4.31448 12.485 4.70312V7.5625C12.485 7.95114 12.1706 8.26562 11.7819 8.26562C11.3933 8.26562 11.0788 7.95114 11.0788 7.5625V4.70312C11.0788 4.31448 11.3933 4 11.7819 4ZM16.2568 6.31211C16.5314 6.03747 16.9764 6.03747 17.2511 6.31211C17.5257 6.58675 17.5257 7.03173 17.2511 7.30638L15.2618 9.29561C14.9872 9.57025 14.5422 9.57025 14.2676 9.29561C13.9929 9.02097 13.9929 8.57598 14.2676 8.30134L16.2568 6.31211ZM8.302 14.2669C8.57664 13.9922 9.02163 13.9922 9.29627 14.2669C9.57091 14.5415 9.57091 14.9865 9.29627 15.2612L7.30703 17.2504C7.03239 17.525 6.58741 17.525 6.31277 17.2504C6.03813 16.9757 6.03813 16.5308 6.31277 16.2561L8.302 14.2669ZM4.70378 11.0781H7.56316C7.9518 11.0781 8.26628 11.3926 8.26628 11.7812C8.26628 12.1699 7.9518 12.4844 7.56316 12.4844H4.70378C4.31514 12.4844 4.00066 12.1699 4.00066 11.7812C4.00066 11.3926 4.31514 11.0781 4.70378 11.0781Z" fill="#77C2C4"/>
</g>
<defs>
<linearGradient id="paint0_linear_22619_2107" x1="19.2109" y1="27.9995" x2="19.2109" y2="10.4229" gradientUnits="userSpaceOnUse">
<stop stop-color="#5F9EA0"/>
<stop offset="1" stop-color="#0F6C6F"/>
</linearGradient>
<clipPath id="clip0_22619_2107">
<rect width="24" height="24" fill="white" transform="matrix(-1 0 0 1 28 4)"/>
</clipPath>
</defs>
</svg>
`;
